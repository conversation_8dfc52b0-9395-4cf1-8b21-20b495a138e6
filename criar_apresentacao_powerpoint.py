#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar uma apresentação PowerPoint com as visualizações geradas
"""

try:
    from pptx import Presentation
    from pptx.util import Inches, Pt
    from pptx.enum.text import PP_ALIGN
    from pptx.dml.color import RGBColor
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

import csv
from collections import Counter
import os

def analisar_dados_resumo():
    """
    Analisa os dados para criar resumo para a apresentação
    """
    arquivo_entrada = "report1750796675025.csv"
    estados_dados = []
    cidades_dados = []
    
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        estados_dados.append(estado)
                        cidades_dados.append(cidade)
    
    except UnicodeDecodeError:
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        estados_dados.append(estado)
                        cidades_dados.append(cidade)
    
    contador_estados = Counter(estados_dados)
    contador_cidades = Counter(cidades_dados)
    
    return contador_estados, contador_cidades

def criar_apresentacao_powerpoint():
    """
    Cria uma apresentação PowerPoint com as análises
    """
    if not PPTX_AVAILABLE:
        print("Biblioteca python-pptx não está disponível.")
        print("Para instalar: pip install python-pptx")
        return False
    
    # Analisa os dados
    contador_estados, contador_cidades = analisar_dados_resumo()
    
    # Cria a apresentação
    prs = Presentation()
    
    # Slide 1: Título
    slide_layout = prs.slide_layouts[0]  # Layout de título
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "Análise Geográfica de Registros"
    subtitle.text = "Distribuição por Cidades e Estados\nRelatório de Análise de Dados"
    
    # Slide 2: Resumo Executivo
    slide_layout = prs.slide_layouts[1]  # Layout de conteúdo
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Resumo Executivo"
    
    # Agrupa por região
    registros_por_regiao = {
        'Sudeste': contador_estados.get('SP', 0) + contador_estados.get('MG', 0) + contador_estados.get('RJ', 0),
        'Sul': contador_estados.get('SC', 0) + contador_estados.get('PR', 0),
        'Centro-Oeste': contador_estados.get('GO', 0) + contador_estados.get('MT', 0) + contador_estados.get('MS', 0) + contador_estados.get('DF', 0),
        'Nordeste': contador_estados.get('PE', 0) + contador_estados.get('CE', 0) + contador_estados.get('PB', 0) + contador_estados.get('SE', 0) + contador_estados.get('BA', 0)
    }
    
    tf = content.text_frame
    tf.text = f"• Total de registros analisados: {sum(contador_estados.values())}"
    
    p = tf.add_paragraph()
    p.text = f"• Estados representados: {len(contador_estados)}"
    
    p = tf.add_paragraph()
    p.text = f"• Cidades únicas: {len(contador_cidades)}"
    
    p = tf.add_paragraph()
    p.text = f"• Concentração regional:"
    
    for regiao, quantidade in sorted(registros_por_regiao.items(), key=lambda x: x[1], reverse=True):
        if quantidade > 0:
            percentual = (quantidade / sum(contador_estados.values())) * 100
            p = tf.add_paragraph()
            p.text = f"  - {regiao}: {quantidade} registros ({percentual:.1f}%)"
            p.level = 1
    
    # Slide 3: Gráfico de Barras
    slide_layout = prs.slide_layouts[6]  # Layout em branco
    slide = prs.slides.add_slide(slide_layout)
    
    # Adiciona título
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "Distribuição de Registros por Estado"
    title_frame.paragraphs[0].font.size = Pt(24)
    title_frame.paragraphs[0].font.bold = True
    
    # Adiciona imagem do gráfico de barras
    if os.path.exists('grafico_barras_estados.png'):
        slide.shapes.add_picture('grafico_barras_estados.png', 
                               Inches(0.5), Inches(1.5), 
                               Inches(9), Inches(5.5))
    
    # Slide 4: Mapa Conceitual
    slide_layout = prs.slide_layouts[6]  # Layout em branco
    slide = prs.slides.add_slide(slide_layout)
    
    # Adiciona título
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "Mapa Conceitual - Distribuição Geográfica"
    title_frame.paragraphs[0].font.size = Pt(24)
    title_frame.paragraphs[0].font.bold = True
    
    # Adiciona imagem do mapa conceitual
    if os.path.exists('mapa_conceitual_brasil.png'):
        slide.shapes.add_picture('mapa_conceitual_brasil.png', 
                               Inches(0.5), Inches(1.5), 
                               Inches(9), Inches(5.5))
    
    # Slide 5: Distribuição por Região
    slide_layout = prs.slide_layouts[6]  # Layout em branco
    slide = prs.slides.add_slide(slide_layout)
    
    # Adiciona título
    title_shape = slide.shapes.add_textbox(Inches(0.5), Inches(0.5), Inches(9), Inches(1))
    title_frame = title_shape.text_frame
    title_frame.text = "Distribuição por Região"
    title_frame.paragraphs[0].font.size = Pt(24)
    title_frame.paragraphs[0].font.bold = True
    
    # Adiciona imagem do gráfico de pizza
    if os.path.exists('grafico_pizza_regioes.png'):
        slide.shapes.add_picture('grafico_pizza_regioes.png', 
                               Inches(0.5), Inches(1.5), 
                               Inches(9), Inches(5.5))
    
    # Slide 6: Top Cidades
    slide_layout = prs.slide_layouts[1]  # Layout de conteúdo
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Top 10 Cidades com Mais Registros"
    
    tf = content.text_frame
    tf.clear()
    
    for i, (cidade, quantidade) in enumerate(contador_cidades.most_common(10), 1):
        p = tf.add_paragraph() if i > 1 else tf.paragraphs[0]
        p.text = f"{i:2d}. {cidade}: {quantidade} registros"
        p.font.size = Pt(16)
    
    # Slide 7: Conclusões
    slide_layout = prs.slide_layouts[1]  # Layout de conteúdo
    slide = prs.slides.add_slide(slide_layout)
    title = slide.shapes.title
    content = slide.placeholders[1]
    
    title.text = "Principais Conclusões"
    
    tf = content.text_frame
    tf.text = "• Concentração regional significativa:"
    
    p = tf.add_paragraph()
    p.text = f"  - Sudeste representa {(registros_por_regiao['Sudeste']/sum(contador_estados.values())*100):.1f}% dos registros"
    p.level = 1
    
    p = tf.add_paragraph()
    p.text = f"  - São Paulo sozinho tem {(contador_estados['SP']/sum(contador_estados.values())*100):.1f}% do total"
    p.level = 1
    
    p = tf.add_paragraph()
    p.text = "• Distribuição geográfica diversificada:"
    
    p = tf.add_paragraph()
    p.text = f"  - Presença em {len(contador_estados)} estados diferentes"
    p.level = 1
    
    p = tf.add_paragraph()
    p.text = f"  - {len(contador_cidades)} cidades únicas representadas"
    p.level = 1
    
    p = tf.add_paragraph()
    p.text = "• Oportunidades de expansão:"
    
    p = tf.add_paragraph()
    p.text = "  - Potencial de crescimento nas regiões Norte e Nordeste"
    p.level = 1
    
    # Salva a apresentação
    prs.save('Apresentacao_Analise_Geografica.pptx')
    return True

def criar_apresentacao_alternativa():
    """
    Cria um arquivo de texto com instruções para criar a apresentação manualmente
    """
    contador_estados, contador_cidades = analisar_dados_resumo()
    
    with open('Instrucoes_Apresentacao_PowerPoint.txt', 'w', encoding='utf-8') as f:
        f.write("=" * 60 + "\n")
        f.write("INSTRUÇÕES PARA CRIAR APRESENTAÇÃO POWERPOINT\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("SLIDE 1 - TÍTULO:\n")
        f.write("-" * 20 + "\n")
        f.write("Título: Análise Geográfica de Registros\n")
        f.write("Subtítulo: Distribuição por Cidades e Estados\n")
        f.write("           Relatório de Análise de Dados\n\n")
        
        f.write("SLIDE 2 - RESUMO EXECUTIVO:\n")
        f.write("-" * 30 + "\n")
        f.write(f"• Total de registros analisados: {sum(contador_estados.values())}\n")
        f.write(f"• Estados representados: {len(contador_estados)}\n")
        f.write(f"• Cidades únicas: {len(contador_cidades)}\n")
        f.write("• Concentração regional:\n")
        
        registros_por_regiao = {
            'Sudeste': contador_estados.get('SP', 0) + contador_estados.get('MG', 0) + contador_estados.get('RJ', 0),
            'Sul': contador_estados.get('SC', 0) + contador_estados.get('PR', 0),
            'Centro-Oeste': contador_estados.get('GO', 0) + contador_estados.get('MT', 0) + contador_estados.get('MS', 0) + contador_estados.get('DF', 0),
            'Nordeste': contador_estados.get('PE', 0) + contador_estados.get('CE', 0) + contador_estados.get('PB', 0) + contador_estados.get('SE', 0) + contador_estados.get('BA', 0)
        }
        
        for regiao, quantidade in sorted(registros_por_regiao.items(), key=lambda x: x[1], reverse=True):
            if quantidade > 0:
                percentual = (quantidade / sum(contador_estados.values())) * 100
                f.write(f"  - {regiao}: {quantidade} registros ({percentual:.1f}%)\n")
        
        f.write("\nSLIDE 3 - GRÁFICO DE BARRAS:\n")
        f.write("-" * 30 + "\n")
        f.write("Título: Distribuição de Registros por Estado\n")
        f.write("Inserir imagem: grafico_barras_estados.png\n\n")
        
        f.write("SLIDE 4 - MAPA CONCEITUAL:\n")
        f.write("-" * 30 + "\n")
        f.write("Título: Mapa Conceitual - Distribuição Geográfica\n")
        f.write("Inserir imagem: mapa_conceitual_brasil.png\n\n")
        
        f.write("SLIDE 5 - DISTRIBUIÇÃO POR REGIÃO:\n")
        f.write("-" * 35 + "\n")
        f.write("Título: Distribuição por Região\n")
        f.write("Inserir imagem: grafico_pizza_regioes.png\n\n")
        
        f.write("SLIDE 6 - TOP CIDADES:\n")
        f.write("-" * 25 + "\n")
        f.write("Título: Top 10 Cidades com Mais Registros\n")
        for i, (cidade, quantidade) in enumerate(contador_cidades.most_common(10), 1):
            f.write(f"{i:2d}. {cidade}: {quantidade} registros\n")
        
        f.write("\nSLIDE 7 - CONCLUSÕES:\n")
        f.write("-" * 25 + "\n")
        f.write("Título: Principais Conclusões\n")
        f.write("• Concentração regional significativa:\n")
        f.write(f"  - Sudeste representa {(registros_por_regiao['Sudeste']/sum(contador_estados.values())*100):.1f}% dos registros\n")
        f.write(f"  - São Paulo sozinho tem {(contador_estados['SP']/sum(contador_estados.values())*100):.1f}% do total\n")
        f.write("• Distribuição geográfica diversificada:\n")
        f.write(f"  - Presença em {len(contador_estados)} estados diferentes\n")
        f.write(f"  - {len(contador_cidades)} cidades únicas representadas\n")
        f.write("• Oportunidades de expansão:\n")
        f.write("  - Potencial de crescimento nas regiões Norte e Nordeste\n")
        
        f.write("\nARQUIVOS DE IMAGEM DISPONÍVEIS:\n")
        f.write("-" * 35 + "\n")
        f.write("1. grafico_barras_estados.png\n")
        f.write("2. mapa_conceitual_brasil.png\n")
        f.write("3. grafico_pizza_regioes.png\n")

def main():
    print("Criando apresentação PowerPoint...")
    
    if criar_apresentacao_powerpoint():
        print("✓ Apresentação PowerPoint criada: Apresentacao_Analise_Geografica.pptx")
    else:
        print("⚠ Não foi possível criar o arquivo PowerPoint automaticamente.")
        print("  Instalando python-pptx...")
        
        # Tenta instalar a biblioteca
        import subprocess
        try:
            subprocess.check_call(['pip', 'install', 'python-pptx'])
            print("✓ python-pptx instalado com sucesso!")
            if criar_apresentacao_powerpoint():
                print("✓ Apresentação PowerPoint criada: Apresentacao_Analise_Geografica.pptx")
            else:
                criar_apresentacao_alternativa()
                print("✓ Instruções criadas: Instrucoes_Apresentacao_PowerPoint.txt")
        except:
            criar_apresentacao_alternativa()
            print("✓ Instruções criadas: Instrucoes_Apresentacao_PowerPoint.txt")
    
    print("\nArquivos disponíveis para sua apresentação:")
    print("  - Apresentacao_Analise_Geografica.pptx (se criado)")
    print("  - Instrucoes_Apresentacao_PowerPoint.txt")
    print("  - grafico_barras_estados.png")
    print("  - mapa_conceitual_brasil.png") 
    print("  - grafico_pizza_regioes.png")
    print("  - relatorio_visual.txt")

if __name__ == "__main__":
    main()
