<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_b4eff4e966d4938d2ec228eb786b5b6d {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
    <div style="position: fixed;
                bottom: 50px; left: 50px; width: 280px; height: 160px;
                background-color: white; border:2px solid grey; z-index:9999;
                font-size:12px; padding: 10px; border-radius: 5px;">
    <p><b>🗺️ Mapa do Brasil - Municípios</b></p>
    <p><b>Estatísticas:</b></p>
    <p>• Total de municípios: 128</p>
    <p>• Total de registros: 235</p>
    <p>• Estados representados: 14</p>
    <p><b>Legenda:</b></p>
    <p>• Tamanho do círculo = Número de registros</p>
    <p>• Cores diferentes = Estados</p>
    <p>• Clique nos pontos para mais detalhes</p>
    </div>
    
    
            <div class="folium-map" id="map_b4eff4e966d4938d2ec228eb786b5b6d" ></div>
        
</body>
<script>
    
    
            var map_b4eff4e966d4938d2ec228eb786b5b6d = L.map(
                "map_b4eff4e966d4938d2ec228eb786b5b6d",
                {
                    center: [-14.235, -51.9253],
                    crs: L.CRS.EPSG3857,
                    ...{
  "maxBounds": [
[
-90,
-180,
],
[
90,
180,
],
],
  "zoom": 5,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_187a1567ea47cf9af1929e435694a0e6 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 4,
  "maxZoom": 12,
  "maxNativeZoom": 12,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_187a1567ea47cf9af1929e435694a0e6.addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
            map_b4eff4e966d4938d2ec228eb786b5b6d.fitBounds(
                [[-33.75, -73.98], [5.27, -34.79]],
                {}
            );
        
    
            var poly_line_32d4750bbe2af78501793c574ffef7f0 = L.polyline(
                [[5.27, -60.23], [4.56, -59.47], [2.82, -51.65], [4.45, -51.65], [1.25, -51.65], [-0.04, -51.11], [-1.55, -48.45], [-2.59, -44.21], [-2.74, -40.35], [-4.56, -37.34], [-5.79, -35.2], [-6.64, -34.79], [-8.05, -34.88], [-9.67, -35.73], [-11.09, -37.46], [-12.58, -38.51], [-15.75, -39.28], [-17.33, -39.41], [-19.83, -39.69], [-20.76, -40.69], [-21.96, -41.59], [-22.91, -43.21], [-23.77, -45.69], [-25.35, -48.65], [-26.24, -48.84], [-28.68, -49.69], [-29.69, -50.15], [-30.11, -51.29], [-31.77, -52.09], [-32.46, -52.37], [-33.75, -53.65], [-33.75, -57.63], [-32.17, -58.15], [-30.17, -57.63], [-27.44, -55.16], [-25.3, -54.62], [-22.87, -55.79], [-20.15, -56.47], [-19.68, -57.85], [-18.04, -57.65], [-16.29, -57.85], [-15.61, -59.52], [-13.69, -60.54], [-12.48, -63.29], [-11.22, -65.33], [-9.82, -66.87], [-9.39, -68.23], [-8.9, -70.09], [-7.52, -72.79], [-7.34, -73.98], [-4.23, -73.98], [-2.31, -71.3], [-0.15, -68.78], [1.72, -67.34], [2.82, -65.59], [3.42, -64.63], [4.17, -62.73], [5.27, -60.23]],
                {"bubblingMouseEvents": true, "color": "#2E8B57", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "#2E8B57", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.8, "smoothFactor": 1.0, "stroke": true, "weight": 3}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_aba06a7a98f2e32ba843a53515f5450d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b47a49167b952d8b032c7980caa9f53f = $(`<div id="html_b47a49167b952d8b032c7980caa9f53f" style="width: 100.0%; height: 100.0%;">Fronteiras do Brasil</div>`)[0];
                popup_aba06a7a98f2e32ba843a53515f5450d.setContent(html_b47a49167b952d8b032c7980caa9f53f);
            
        

        poly_line_32d4750bbe2af78501793c574ffef7f0.bindPopup(popup_aba06a7a98f2e32ba843a53515f5450d)
        ;

        
    
    
            poly_line_32d4750bbe2af78501793c574ffef7f0.bindTooltip(
                `<div>
                     Perímetro brasileiro
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_889538734083e8f3faa9a34a83b7cd00 = L.circleMarker(
                [-23.4538, -46.5333],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 24, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_ac3763f6d47c4c37a768a186e4e355fa = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c5501c6113efbc5b80d0e88c6f7e8652 = $(`<div id="html_c5501c6113efbc5b80d0e88c6f7e8652" style="width: 100.0%; height: 100.0%;"><b>GUARULHOS (SP)</b><br>8 registros</div>`)[0];
                popup_ac3763f6d47c4c37a768a186e4e355fa.setContent(html_c5501c6113efbc5b80d0e88c6f7e8652);
            
        

        circle_marker_889538734083e8f3faa9a34a83b7cd00.bindPopup(popup_ac3763f6d47c4c37a768a186e4e355fa)
        ;

        
    
    
            circle_marker_889538734083e8f3faa9a34a83b7cd00.bindTooltip(
                `<div>
                     GUARULHOS: 8
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_99e205b3f9a1d36a9bd8665400062f5b = L.circleMarker(
                [-22.9456, -47.3156],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a984da06e9ec05704c2cfe3cbc640627 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_205c75e6c6ae516307c570f1f83688b8 = $(`<div id="html_205c75e6c6ae516307c570f1f83688b8" style="width: 100.0%; height: 100.0%;"><b>MONTE MOR (SP)</b><br>2 registros</div>`)[0];
                popup_a984da06e9ec05704c2cfe3cbc640627.setContent(html_205c75e6c6ae516307c570f1f83688b8);
            
        

        circle_marker_99e205b3f9a1d36a9bd8665400062f5b.bindPopup(popup_a984da06e9ec05704c2cfe3cbc640627)
        ;

        
    
    
            circle_marker_99e205b3f9a1d36a9bd8665400062f5b.bindTooltip(
                `<div>
                     MONTE MOR: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_338c59ff40db7830bf0ea850df6e40f8 = L.circleMarker(
                [-23.0922, -47.2181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_378f7be3876abd912da731821ee3b4e1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fb2a4f9ebc14415ae585c8ce51c69871 = $(`<div id="html_fb2a4f9ebc14415ae585c8ce51c69871" style="width: 100.0%; height: 100.0%;"><b>INDAIATUBA (SP)</b><br>5 registros</div>`)[0];
                popup_378f7be3876abd912da731821ee3b4e1.setContent(html_fb2a4f9ebc14415ae585c8ce51c69871);
            
        

        circle_marker_338c59ff40db7830bf0ea850df6e40f8.bindPopup(popup_378f7be3876abd912da731821ee3b4e1)
        ;

        
    
    
            circle_marker_338c59ff40db7830bf0ea850df6e40f8.bindTooltip(
                `<div>
                     INDAIATUBA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_72676ab4865b489203b877c2860aa49c = L.circleMarker(
                [-23.5015, -47.4526],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 30, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_665d6c01029f572c22b799b77b1f019e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_35ffbc9738ca39cd89c00129b00fe5bc = $(`<div id="html_35ffbc9738ca39cd89c00129b00fe5bc" style="width: 100.0%; height: 100.0%;"><b>SOROCABA (SP)</b><br>10 registros</div>`)[0];
                popup_665d6c01029f572c22b799b77b1f019e.setContent(html_35ffbc9738ca39cd89c00129b00fe5bc);
            
        

        circle_marker_72676ab4865b489203b877c2860aa49c.bindPopup(popup_665d6c01029f572c22b799b77b1f019e)
        ;

        
    
    
            circle_marker_72676ab4865b489203b877c2860aa49c.bindTooltip(
                `<div>
                     SOROCABA: 10
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5566ef4a93d2e4131a9f8a430081c683 = L.circleMarker(
                [-23.2156, -47.5281],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_bcdf1e52ae2184f0248ba78810ac607f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9cc2e788cb7a067ecc3d502dd91c4172 = $(`<div id="html_9cc2e788cb7a067ecc3d502dd91c4172" style="width: 100.0%; height: 100.0%;"><b>PORTO FELIZ (SP)</b><br>1 registros</div>`)[0];
                popup_bcdf1e52ae2184f0248ba78810ac607f.setContent(html_9cc2e788cb7a067ecc3d502dd91c4172);
            
        

        circle_marker_5566ef4a93d2e4131a9f8a430081c683.bindPopup(popup_bcdf1e52ae2184f0248ba78810ac607f)
        ;

        
    
    
            circle_marker_5566ef4a93d2e4131a9f8a430081c683.bindTooltip(
                `<div>
                     PORTO FELIZ: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_78bae007ac7f23e79cc3d58a5deb6289 = L.circleMarker(
                [-23.3531, -47.8656],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_ff9071c723c38c5a36859a5047357c82 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_03403f7d64335977624afa5d02d986b3 = $(`<div id="html_03403f7d64335977624afa5d02d986b3" style="width: 100.0%; height: 100.0%;"><b>TATUI (SP)</b><br>2 registros</div>`)[0];
                popup_ff9071c723c38c5a36859a5047357c82.setContent(html_03403f7d64335977624afa5d02d986b3);
            
        

        circle_marker_78bae007ac7f23e79cc3d58a5deb6289.bindPopup(popup_ff9071c723c38c5a36859a5047357c82)
        ;

        
    
    
            circle_marker_78bae007ac7f23e79cc3d58a5deb6289.bindTooltip(
                `<div>
                     TATUI: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c165ee1d55ab9ce0c21e2ea1618d899a = L.circleMarker(
                [-23.2831, -47.6731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_42e8c0431d1c08f299c08221e34284d9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_864dbc7b7bfb1226f9191b5f7d179ec5 = $(`<div id="html_864dbc7b7bfb1226f9191b5f7d179ec5" style="width: 100.0%; height: 100.0%;"><b>BOITUVA (SP)</b><br>4 registros</div>`)[0];
                popup_42e8c0431d1c08f299c08221e34284d9.setContent(html_864dbc7b7bfb1226f9191b5f7d179ec5);
            
        

        circle_marker_c165ee1d55ab9ce0c21e2ea1618d899a.bindPopup(popup_42e8c0431d1c08f299c08221e34284d9)
        ;

        
    
    
            circle_marker_c165ee1d55ab9ce0c21e2ea1618d899a.bindTooltip(
                `<div>
                     BOITUVA: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6c693a6e5106947f1bb6fe7d4993584f = L.circleMarker(
                [-22.9056, -47.0608],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 27, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_4d83d19be85db6bc91f4aee08264304e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8ba549151dec8157e369c2dd820f6ac8 = $(`<div id="html_8ba549151dec8157e369c2dd820f6ac8" style="width: 100.0%; height: 100.0%;"><b>CAMPINAS (SP)</b><br>9 registros</div>`)[0];
                popup_4d83d19be85db6bc91f4aee08264304e.setContent(html_8ba549151dec8157e369c2dd820f6ac8);
            
        

        circle_marker_6c693a6e5106947f1bb6fe7d4993584f.bindPopup(popup_4d83d19be85db6bc91f4aee08264304e)
        ;

        
    
    
            circle_marker_6c693a6e5106947f1bb6fe7d4993584f.bindTooltip(
                `<div>
                     CAMPINAS: 9
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4842f58c2eba714e1deb710012daa277 = L.circleMarker(
                [-21.5931, -48.8156],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c37a1a567c2a501c5f53260937658616 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_753b270c84447631821cd4555d6149c2 = $(`<div id="html_753b270c84447631821cd4555d6149c2" style="width: 100.0%; height: 100.0%;"><b>ITAPOLIS (SP)</b><br>1 registros</div>`)[0];
                popup_c37a1a567c2a501c5f53260937658616.setContent(html_753b270c84447631821cd4555d6149c2);
            
        

        circle_marker_4842f58c2eba714e1deb710012daa277.bindPopup(popup_c37a1a567c2a501c5f53260937658616)
        ;

        
    
    
            circle_marker_4842f58c2eba714e1deb710012daa277.bindTooltip(
                `<div>
                     ITAPOLIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_55bb7f285e81cfbcbc92eee0fc0ae681 = L.circleMarker(
                [-21.7947, -48.1756],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_46d463ae8f0741c614e33c19d5fd6fbd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2daa4f2e0f5e590e225f145077b236de = $(`<div id="html_2daa4f2e0f5e590e225f145077b236de" style="width: 100.0%; height: 100.0%;"><b>ARARAQUARA (SP)</b><br>5 registros</div>`)[0];
                popup_46d463ae8f0741c614e33c19d5fd6fbd.setContent(html_2daa4f2e0f5e590e225f145077b236de);
            
        

        circle_marker_55bb7f285e81cfbcbc92eee0fc0ae681.bindPopup(popup_46d463ae8f0741c614e33c19d5fd6fbd)
        ;

        
    
    
            circle_marker_55bb7f285e81cfbcbc92eee0fc0ae681.bindTooltip(
                `<div>
                     ARARAQUARA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3981beeb06d4162ecae0f97ac1db95f1 = L.circleMarker(
                [-23.5505, -46.6333],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 45, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_54997ec30baeb1bb6f5423ad91ec02bb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f5b4e96405f2ea15a3c49a300d2cd444 = $(`<div id="html_f5b4e96405f2ea15a3c49a300d2cd444" style="width: 100.0%; height: 100.0%;"><b>SAO PAULO (SP)</b><br>15 registros</div>`)[0];
                popup_54997ec30baeb1bb6f5423ad91ec02bb.setContent(html_f5b4e96405f2ea15a3c49a300d2cd444);
            
        

        circle_marker_3981beeb06d4162ecae0f97ac1db95f1.bindPopup(popup_54997ec30baeb1bb6f5423ad91ec02bb)
        ;

        
    
    
            circle_marker_3981beeb06d4162ecae0f97ac1db95f1.bindTooltip(
                `<div>
                     SAO PAULO: 15
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_eef8732672ecf30d6479348356d2130d = L.circleMarker(
                [-22.7394, -47.3314],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_f9e453a82a1b38769b3d843344df158b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_50cd09e538b5be7a2d6a47315f1c423b = $(`<div id="html_50cd09e538b5be7a2d6a47315f1c423b" style="width: 100.0%; height: 100.0%;"><b>AMERICANA (SP)</b><br>1 registros</div>`)[0];
                popup_f9e453a82a1b38769b3d843344df158b.setContent(html_50cd09e538b5be7a2d6a47315f1c423b);
            
        

        circle_marker_eef8732672ecf30d6479348356d2130d.bindPopup(popup_f9e453a82a1b38769b3d843344df158b)
        ;

        
    
    
            circle_marker_eef8732672ecf30d6479348356d2130d.bindTooltip(
                `<div>
                     AMERICANA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_caf032d9756851bb6f0bd5210d2408b9 = L.circleMarker(
                [-22.5731, -47.1731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_edc366b04d748d669049d049d2a5e7cb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a8934c3473e3ab837e04e6d5e0cb7bd7 = $(`<div id="html_a8934c3473e3ab837e04e6d5e0cb7bd7" style="width: 100.0%; height: 100.0%;"><b>ARTUR NOGUEIRA (SP)</b><br>2 registros</div>`)[0];
                popup_edc366b04d748d669049d049d2a5e7cb.setContent(html_a8934c3473e3ab837e04e6d5e0cb7bd7);
            
        

        circle_marker_caf032d9756851bb6f0bd5210d2408b9.bindPopup(popup_edc366b04d748d669049d049d2a5e7cb)
        ;

        
    
    
            circle_marker_caf032d9756851bb6f0bd5210d2408b9.bindTooltip(
                `<div>
                     ARTUR NOGUEIRA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8f09f278a1788ce62ec544322966f4f0 = L.circleMarker(
                [-22.6381, -47.0531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_5d649460605caa3586239a8b819553de = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4990503526e668d717c2b19458b9ee2c = $(`<div id="html_4990503526e668d717c2b19458b9ee2c" style="width: 100.0%; height: 100.0%;"><b>HOLAMBRA (SP)</b><br>1 registros</div>`)[0];
                popup_5d649460605caa3586239a8b819553de.setContent(html_4990503526e668d717c2b19458b9ee2c);
            
        

        circle_marker_8f09f278a1788ce62ec544322966f4f0.bindPopup(popup_5d649460605caa3586239a8b819553de)
        ;

        
    
    
            circle_marker_8f09f278a1788ce62ec544322966f4f0.bindTooltip(
                `<div>
                     HOLAMBRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3087a6e32714922ca8be5f5e88786fa9 = L.circleMarker(
                [-20.2831, -50.2431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_f51659d3bacf12e752ea08d4717f2c6c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8d22daf8f667a8858bd1250b6d186e8e = $(`<div id="html_8d22daf8f667a8858bd1250b6d186e8e" style="width: 100.0%; height: 100.0%;"><b>FERNANDOPOLIS (SP)</b><br>2 registros</div>`)[0];
                popup_f51659d3bacf12e752ea08d4717f2c6c.setContent(html_8d22daf8f667a8858bd1250b6d186e8e);
            
        

        circle_marker_3087a6e32714922ca8be5f5e88786fa9.bindPopup(popup_f51659d3bacf12e752ea08d4717f2c6c)
        ;

        
    
    
            circle_marker_3087a6e32714922ca8be5f5e88786fa9.bindTooltip(
                `<div>
                     FERNANDOPOLIS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7c7d3d7673ce1ecbf6b0583eca564122 = L.circleMarker(
                [-22.3581, -47.3831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_59e005366a5c081eec1a991b750d29b8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d75e0811a4563664acce3b4c15fccee0 = $(`<div id="html_d75e0811a4563664acce3b4c15fccee0" style="width: 100.0%; height: 100.0%;"><b>ARARAS (SP)</b><br>3 registros</div>`)[0];
                popup_59e005366a5c081eec1a991b750d29b8.setContent(html_d75e0811a4563664acce3b4c15fccee0);
            
        

        circle_marker_7c7d3d7673ce1ecbf6b0583eca564122.bindPopup(popup_59e005366a5c081eec1a991b750d29b8)
        ;

        
    
    
            circle_marker_7c7d3d7673ce1ecbf6b0583eca564122.bindTooltip(
                `<div>
                     ARARAS: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9378cc274d105e255bf41dc522376d7b = L.circleMarker(
                [-25.4284, -49.2733],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_734d4a63a62696f8bbba2edc5fe53d93 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_78f2378ef8dd91785cc4ef970b8ddc98 = $(`<div id="html_78f2378ef8dd91785cc4ef970b8ddc98" style="width: 100.0%; height: 100.0%;"><b>CURITIBA (PR)</b><br>2 registros</div>`)[0];
                popup_734d4a63a62696f8bbba2edc5fe53d93.setContent(html_78f2378ef8dd91785cc4ef970b8ddc98);
            
        

        circle_marker_9378cc274d105e255bf41dc522376d7b.bindPopup(popup_734d4a63a62696f8bbba2edc5fe53d93)
        ;

        
    
    
            circle_marker_9378cc274d105e255bf41dc522376d7b.bindTooltip(
                `<div>
                     CURITIBA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a6c79ef425237f0df56121088fcc0c49 = L.circleMarker(
                [-23.1864, -46.8842],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a73b01c8050452107c1c8660a6e99cc3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5f195ea9ee74f4ceb9c2b4022f1fb9f0 = $(`<div id="html_5f195ea9ee74f4ceb9c2b4022f1fb9f0" style="width: 100.0%; height: 100.0%;"><b>JUNDIAI (SP)</b><br>5 registros</div>`)[0];
                popup_a73b01c8050452107c1c8660a6e99cc3.setContent(html_5f195ea9ee74f4ceb9c2b4022f1fb9f0);
            
        

        circle_marker_a6c79ef425237f0df56121088fcc0c49.bindPopup(popup_a73b01c8050452107c1c8660a6e99cc3)
        ;

        
    
    
            circle_marker_a6c79ef425237f0df56121088fcc0c49.bindTooltip(
                `<div>
                     JUNDIAI: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_86a65445ae5f32aa9e55f543305a4b4b = L.circleMarker(
                [-23.8131, -47.7131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_f2aa22205bcea8a369b7b2af09886006 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_23bafe5efdc1b19ea84416f5374708e2 = $(`<div id="html_23bafe5efdc1b19ea84416f5374708e2" style="width: 100.0%; height: 100.0%;"><b>PILAR DO SUL (SP)</b><br>1 registros</div>`)[0];
                popup_f2aa22205bcea8a369b7b2af09886006.setContent(html_23bafe5efdc1b19ea84416f5374708e2);
            
        

        circle_marker_86a65445ae5f32aa9e55f543305a4b4b.bindPopup(popup_f2aa22205bcea8a369b7b2af09886006)
        ;

        
    
    
            circle_marker_86a65445ae5f32aa9e55f543305a4b4b.bindTooltip(
                `<div>
                     PILAR DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a2408c6ff750df9817f39494c6e661f7 = L.circleMarker(
                [-23.0381, -47.8381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_84743fcf017225e2572d15da798cc5c0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_86d24b1a328e064b65b2a135747992a7 = $(`<div id="html_86d24b1a328e064b65b2a135747992a7" style="width: 100.0%; height: 100.0%;"><b>LARANJAL PAULISTA (SP)</b><br>1 registros</div>`)[0];
                popup_84743fcf017225e2572d15da798cc5c0.setContent(html_86d24b1a328e064b65b2a135747992a7);
            
        

        circle_marker_a2408c6ff750df9817f39494c6e661f7.bindPopup(popup_84743fcf017225e2572d15da798cc5c0)
        ;

        
    
    
            circle_marker_a2408c6ff750df9817f39494c6e661f7.bindTooltip(
                `<div>
                     LARANJAL PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0493b815b401b2ff3fea12febb4500f5 = L.circleMarker(
                [-23.5106, -46.8761],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_e21a497cf4a67af7aa027e1e1641cb27 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5d918ebb812ece045e0a15aa44dd4e1a = $(`<div id="html_5d918ebb812ece045e0a15aa44dd4e1a" style="width: 100.0%; height: 100.0%;"><b>BARUERI (SP)</b><br>1 registros</div>`)[0];
                popup_e21a497cf4a67af7aa027e1e1641cb27.setContent(html_5d918ebb812ece045e0a15aa44dd4e1a);
            
        

        circle_marker_0493b815b401b2ff3fea12febb4500f5.bindPopup(popup_e21a497cf4a67af7aa027e1e1641cb27)
        ;

        
    
    
            circle_marker_0493b815b401b2ff3fea12febb4500f5.bindTooltip(
                `<div>
                     BARUERI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f4d31ad0654218dc1dfb24157372d0bd = L.circleMarker(
                [-20.8881, -47.5881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_43ce3f781a380e5c9957e8c309da3ae9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f4c826ac249d112d6adf1968a1426d32 = $(`<div id="html_f4c826ac249d112d6adf1968a1426d32" style="width: 100.0%; height: 100.0%;"><b>BATATAIS (SP)</b><br>1 registros</div>`)[0];
                popup_43ce3f781a380e5c9957e8c309da3ae9.setContent(html_f4c826ac249d112d6adf1968a1426d32);
            
        

        circle_marker_f4d31ad0654218dc1dfb24157372d0bd.bindPopup(popup_43ce3f781a380e5c9957e8c309da3ae9)
        ;

        
    
    
            circle_marker_f4d31ad0654218dc1dfb24157372d0bd.bindTooltip(
                `<div>
                     BATATAIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0748017592eed00679123981f0a7ff15 = L.circleMarker(
                [-21.0531, -49.6881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_664312511a87374daaca58c0ccba4fea = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d1b35c03edece95c197366eb68b73880 = $(`<div id="html_d1b35c03edece95c197366eb68b73880" style="width: 100.0%; height: 100.0%;"><b>JOSE BONIFACIO (SP)</b><br>1 registros</div>`)[0];
                popup_664312511a87374daaca58c0ccba4fea.setContent(html_d1b35c03edece95c197366eb68b73880);
            
        

        circle_marker_0748017592eed00679123981f0a7ff15.bindPopup(popup_664312511a87374daaca58c0ccba4fea)
        ;

        
    
    
            circle_marker_0748017592eed00679123981f0a7ff15.bindTooltip(
                `<div>
                     JOSE BONIFACIO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6e8166cc3cbd8441c35ecace316bb767 = L.circleMarker(
                [-20.5386, -47.4006],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_b2485845dfc44d61a1dd74e260b130a0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f4e63183ba870ab89f60ce2fc18f9152 = $(`<div id="html_f4e63183ba870ab89f60ce2fc18f9152" style="width: 100.0%; height: 100.0%;"><b>FRANCA (SP)</b><br>2 registros</div>`)[0];
                popup_b2485845dfc44d61a1dd74e260b130a0.setContent(html_f4e63183ba870ab89f60ce2fc18f9152);
            
        

        circle_marker_6e8166cc3cbd8441c35ecace316bb767.bindPopup(popup_b2485845dfc44d61a1dd74e260b130a0)
        ;

        
    
    
            circle_marker_6e8166cc3cbd8441c35ecace316bb767.bindTooltip(
                `<div>
                     FRANCA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_32e052532a6ba62e55c3130fe8c2da5d = L.circleMarker(
                [-22.5647, -47.4017],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_86f98895d140249e5698f7c70efbd14f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_975ac1fab8320704cca857030af8249a = $(`<div id="html_975ac1fab8320704cca857030af8249a" style="width: 100.0%; height: 100.0%;"><b>LIMEIRA (SP)</b><br>1 registros</div>`)[0];
                popup_86f98895d140249e5698f7c70efbd14f.setContent(html_975ac1fab8320704cca857030af8249a);
            
        

        circle_marker_32e052532a6ba62e55c3130fe8c2da5d.bindPopup(popup_86f98895d140249e5698f7c70efbd14f)
        ;

        
    
    
            circle_marker_32e052532a6ba62e55c3130fe8c2da5d.bindTooltip(
                `<div>
                     LIMEIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_be1c16907fbb4db6b0c6dc3354c37676 = L.circleMarker(
                [-22.7031, -46.9881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_0b37f1a8c0649c0bcd4a2e5f5f178b51 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c72785ac5cc9587fd93005de77af7558 = $(`<div id="html_c72785ac5cc9587fd93005de77af7558" style="width: 100.0%; height: 100.0%;"><b>JAGUARIUNA (SP)</b><br>1 registros</div>`)[0];
                popup_0b37f1a8c0649c0bcd4a2e5f5f178b51.setContent(html_c72785ac5cc9587fd93005de77af7558);
            
        

        circle_marker_be1c16907fbb4db6b0c6dc3354c37676.bindPopup(popup_0b37f1a8c0649c0bcd4a2e5f5f178b51)
        ;

        
    
    
            circle_marker_be1c16907fbb4db6b0c6dc3354c37676.bindTooltip(
                `<div>
                     JAGUARIUNA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_062899a1d3a38d40e8357d02127b578b = L.circleMarker(
                [-23.6629, -46.5383],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c2b09074d75f6e3db04e9334a749bb55 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_791d209af9d786a8131179fd3d8bb368 = $(`<div id="html_791d209af9d786a8131179fd3d8bb368" style="width: 100.0%; height: 100.0%;"><b>SANTO ANDRE (SP)</b><br>1 registros</div>`)[0];
                popup_c2b09074d75f6e3db04e9334a749bb55.setContent(html_791d209af9d786a8131179fd3d8bb368);
            
        

        circle_marker_062899a1d3a38d40e8357d02127b578b.bindPopup(popup_c2b09074d75f6e3db04e9334a749bb55)
        ;

        
    
    
            circle_marker_062899a1d3a38d40e8357d02127b578b.bindTooltip(
                `<div>
                     SANTO ANDRE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a3b6b10bb8b116186228d88635f7de12 = L.circleMarker(
                [-26.3044, -48.8456],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_cddff41d417bec8643a1745a78272091 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2163cac26e59e13175af4f748aba7daf = $(`<div id="html_2163cac26e59e13175af4f748aba7daf" style="width: 100.0%; height: 100.0%;"><b>JOINVILLE (SC)</b><br>5 registros</div>`)[0];
                popup_cddff41d417bec8643a1745a78272091.setContent(html_2163cac26e59e13175af4f748aba7daf);
            
        

        circle_marker_a3b6b10bb8b116186228d88635f7de12.bindPopup(popup_cddff41d417bec8643a1745a78272091)
        ;

        
    
    
            circle_marker_a3b6b10bb8b116186228d88635f7de12.bindTooltip(
                `<div>
                     JOINVILLE: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7a1e82347c204f89cbf6c3c6a3da4ae7 = L.circleMarker(
                [-23.1794, -45.8869],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 21, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_23a6df7df826f73891fbc1fa48e55a42 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9734cd6509ff63239ac8a2949de8bb6a = $(`<div id="html_9734cd6509ff63239ac8a2949de8bb6a" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DOS CAMPOS (SP)</b><br>7 registros</div>`)[0];
                popup_23a6df7df826f73891fbc1fa48e55a42.setContent(html_9734cd6509ff63239ac8a2949de8bb6a);
            
        

        circle_marker_7a1e82347c204f89cbf6c3c6a3da4ae7.bindPopup(popup_23a6df7df826f73891fbc1fa48e55a42)
        ;

        
    
    
            circle_marker_7a1e82347c204f89cbf6c3c6a3da4ae7.bindTooltip(
                `<div>
                     SAO JOSE DOS CAMPOS: 7
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_557fd34d0bbe708438760c4a3994c596 = L.circleMarker(
                [-22.4114, -47.5614],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_79782e9ff9b5927d1ce2614939cf182e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c2c641215c1a17ae9f04441fd72c3727 = $(`<div id="html_c2c641215c1a17ae9f04441fd72c3727" style="width: 100.0%; height: 100.0%;"><b>RIO CLARO (SP)</b><br>2 registros</div>`)[0];
                popup_79782e9ff9b5927d1ce2614939cf182e.setContent(html_c2c641215c1a17ae9f04441fd72c3727);
            
        

        circle_marker_557fd34d0bbe708438760c4a3994c596.bindPopup(popup_79782e9ff9b5927d1ce2614939cf182e)
        ;

        
    
    
            circle_marker_557fd34d0bbe708438760c4a3994c596.bindTooltip(
                `<div>
                     RIO CLARO: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_dc8a4503e70c37ff6199fe3e09c8c3c7 = L.circleMarker(
                [-23.5225, -46.1883],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_6e45fd1375c51f4ba7614e17d170aca4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_11e11015e1195dad787447ca1c98db46 = $(`<div id="html_11e11015e1195dad787447ca1c98db46" style="width: 100.0%; height: 100.0%;"><b>MOJI DAS CRUZES (SP)</b><br>1 registros</div>`)[0];
                popup_6e45fd1375c51f4ba7614e17d170aca4.setContent(html_11e11015e1195dad787447ca1c98db46);
            
        

        circle_marker_dc8a4503e70c37ff6199fe3e09c8c3c7.bindPopup(popup_6e45fd1375c51f4ba7614e17d170aca4)
        ;

        
    
    
            circle_marker_dc8a4503e70c37ff6199fe3e09c8c3c7.bindTooltip(
                `<div>
                     MOJI DAS CRUZES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_27f04b6e968d24807f2b7313d214ada1 = L.circleMarker(
                [-24.1831, -46.7881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_3f008889b1a2df9b0af68d576e8fbf01 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8d631ae98f662dd26a28ef9e9652139f = $(`<div id="html_8d631ae98f662dd26a28ef9e9652139f" style="width: 100.0%; height: 100.0%;"><b>ITANHAEM (SP)</b><br>1 registros</div>`)[0];
                popup_3f008889b1a2df9b0af68d576e8fbf01.setContent(html_8d631ae98f662dd26a28ef9e9652139f);
            
        

        circle_marker_27f04b6e968d24807f2b7313d214ada1.bindPopup(popup_3f008889b1a2df9b0af68d576e8fbf01)
        ;

        
    
    
            circle_marker_27f04b6e968d24807f2b7313d214ada1.bindTooltip(
                `<div>
                     ITANHAEM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_135b2f8bc5cb0abefeb4a0fbc485ee1b = L.circleMarker(
                [-21.1775, -47.8103],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_3bb0e679941d7a386059debdb7ce72ee = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0f4ed2aa45af75c308aac47de8fae4f8 = $(`<div id="html_0f4ed2aa45af75c308aac47de8fae4f8" style="width: 100.0%; height: 100.0%;"><b>RIBEIRAO PRETO (SP)</b><br>2 registros</div>`)[0];
                popup_3bb0e679941d7a386059debdb7ce72ee.setContent(html_0f4ed2aa45af75c308aac47de8fae4f8);
            
        

        circle_marker_135b2f8bc5cb0abefeb4a0fbc485ee1b.bindPopup(popup_3bb0e679941d7a386059debdb7ce72ee)
        ;

        
    
    
            circle_marker_135b2f8bc5cb0abefeb4a0fbc485ee1b.bindTooltip(
                `<div>
                     RIBEIRAO PRETO: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_96bfa3fbeedf2794ffe2915f317b7ab5 = L.circleMarker(
                [-20.8197, -49.3794],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_e4906073d99678d079bbbc59f5b6c88e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4c5b6d467aabec8482778aee34f32146 = $(`<div id="html_4c5b6d467aabec8482778aee34f32146" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DO RIO PRETO (SP)</b><br>3 registros</div>`)[0];
                popup_e4906073d99678d079bbbc59f5b6c88e.setContent(html_4c5b6d467aabec8482778aee34f32146);
            
        

        circle_marker_96bfa3fbeedf2794ffe2915f317b7ab5.bindPopup(popup_e4906073d99678d079bbbc59f5b6c88e)
        ;

        
    
    
            circle_marker_96bfa3fbeedf2794ffe2915f317b7ab5.bindTooltip(
                `<div>
                     SAO JOSE DO RIO PRETO: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_08bd7d8585939a469ed340b0b1053e81 = L.circleMarker(
                [-23.9608, -46.3331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_9d99c0f046a3eddd9cc3bb7475e1128f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5fc7bed29fa1f6a04c54825f7826abc9 = $(`<div id="html_5fc7bed29fa1f6a04c54825f7826abc9" style="width: 100.0%; height: 100.0%;"><b>SANTOS (SP)</b><br>3 registros</div>`)[0];
                popup_9d99c0f046a3eddd9cc3bb7475e1128f.setContent(html_5fc7bed29fa1f6a04c54825f7826abc9);
            
        

        circle_marker_08bd7d8585939a469ed340b0b1053e81.bindPopup(popup_9d99c0f046a3eddd9cc3bb7475e1128f)
        ;

        
    
    
            circle_marker_08bd7d8585939a469ed340b0b1053e81.bindTooltip(
                `<div>
                     SANTOS: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3d16b25c6a3afb161440693f0255b2ee = L.circleMarker(
                [-24.0931, -46.6181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_92c4e1045cebdded4c396c72d69744b2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_52473d9cc5203cea7d18c4a50250eb48 = $(`<div id="html_52473d9cc5203cea7d18c4a50250eb48" style="width: 100.0%; height: 100.0%;"><b>MONGAGUA (SP)</b><br>3 registros</div>`)[0];
                popup_92c4e1045cebdded4c396c72d69744b2.setContent(html_52473d9cc5203cea7d18c4a50250eb48);
            
        

        circle_marker_3d16b25c6a3afb161440693f0255b2ee.bindPopup(popup_92c4e1045cebdded4c396c72d69744b2)
        ;

        
    
    
            circle_marker_3d16b25c6a3afb161440693f0255b2ee.bindTooltip(
                `<div>
                     MONGAGUA: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ea2e67c1fdd75515e788bd01439f50de = L.circleMarker(
                [-24.3181, -46.9981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_2eafd947c4ff32d90c4bb2eb6e8e3ad9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_dabdd8d937da593556b7c6d6501c6b3e = $(`<div id="html_dabdd8d937da593556b7c6d6501c6b3e" style="width: 100.0%; height: 100.0%;"><b>PERUIBE (SP)</b><br>2 registros</div>`)[0];
                popup_2eafd947c4ff32d90c4bb2eb6e8e3ad9.setContent(html_dabdd8d937da593556b7c6d6501c6b3e);
            
        

        circle_marker_ea2e67c1fdd75515e788bd01439f50de.bindPopup(popup_2eafd947c4ff32d90c4bb2eb6e8e3ad9)
        ;

        
    
    
            circle_marker_ea2e67c1fdd75515e788bd01439f50de.bindTooltip(
                `<div>
                     PERUIBE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_65d04fb43e545a888c08ca15a1063fe8 = L.circleMarker(
                [-20.7181, -47.8881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_4057ab840021f5d27d13ce3a908bb5c5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1eef587da6b990e0dca9804a672c7b12 = $(`<div id="html_1eef587da6b990e0dca9804a672c7b12" style="width: 100.0%; height: 100.0%;"><b>ORLANDIA (SP)</b><br>1 registros</div>`)[0];
                popup_4057ab840021f5d27d13ce3a908bb5c5.setContent(html_1eef587da6b990e0dca9804a672c7b12);
            
        

        circle_marker_65d04fb43e545a888c08ca15a1063fe8.bindPopup(popup_4057ab840021f5d27d13ce3a908bb5c5)
        ;

        
    
    
            circle_marker_65d04fb43e545a888c08ca15a1063fe8.bindTooltip(
                `<div>
                     ORLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_55e727b7ce6f5c749b63a2c062969c19 = L.circleMarker(
                [-22.8219, -47.2669],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_598759b11e8c298f204418683c00bae2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d8067d1a697442d684351afe1b2963c9 = $(`<div id="html_d8067d1a697442d684351afe1b2963c9" style="width: 100.0%; height: 100.0%;"><b>SUMARE (SP)</b><br>2 registros</div>`)[0];
                popup_598759b11e8c298f204418683c00bae2.setContent(html_d8067d1a697442d684351afe1b2963c9);
            
        

        circle_marker_55e727b7ce6f5c749b63a2c062969c19.bindPopup(popup_598759b11e8c298f204418683c00bae2)
        ;

        
    
    
            circle_marker_55e727b7ce6f5c749b63a2c062969c19.bindTooltip(
                `<div>
                     SUMARE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_da826e06a7a29be28461a2683c8ed4d4 = L.circleMarker(
                [-21.5931, -46.8931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a0aa871c9ea70b4146aa611169ac5dbb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9fb07b6ddf515548859b4f1ee8aaa00d = $(`<div id="html_9fb07b6ddf515548859b4f1ee8aaa00d" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DO RIO PARDO (SP)</b><br>1 registros</div>`)[0];
                popup_a0aa871c9ea70b4146aa611169ac5dbb.setContent(html_9fb07b6ddf515548859b4f1ee8aaa00d);
            
        

        circle_marker_da826e06a7a29be28461a2683c8ed4d4.bindPopup(popup_a0aa871c9ea70b4146aa611169ac5dbb)
        ;

        
    
    
            circle_marker_da826e06a7a29be28461a2683c8ed4d4.bindTooltip(
                `<div>
                     SAO JOSE DO RIO PARDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b33e40a598f1c488f033a2971b2d4462 = L.circleMarker(
                [-24.1581, -49.8231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_d6c676cfc130b0f112a6d787c1774589 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e596306419f372b7a251eb65adccfbfc = $(`<div id="html_e596306419f372b7a251eb65adccfbfc" style="width: 100.0%; height: 100.0%;"><b>ARAPOTI (PR)</b><br>1 registros</div>`)[0];
                popup_d6c676cfc130b0f112a6d787c1774589.setContent(html_e596306419f372b7a251eb65adccfbfc);
            
        

        circle_marker_b33e40a598f1c488f033a2971b2d4462.bindPopup(popup_d6c676cfc130b0f112a6d787c1774589)
        ;

        
    
    
            circle_marker_b33e40a598f1c488f033a2971b2d4462.bindTooltip(
                `<div>
                     ARAPOTI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_08e2e537696e444eb2a22c1513d8a27b = L.circleMarker(
                [-27.2142, -49.6431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c4205c64ba751554bba2c7acbdd162c2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0afb8475742e0c576c13ad0e85f9f764 = $(`<div id="html_0afb8475742e0c576c13ad0e85f9f764" style="width: 100.0%; height: 100.0%;"><b>RIO DO SUL (SC)</b><br>2 registros</div>`)[0];
                popup_c4205c64ba751554bba2c7acbdd162c2.setContent(html_0afb8475742e0c576c13ad0e85f9f764);
            
        

        circle_marker_08e2e537696e444eb2a22c1513d8a27b.bindPopup(popup_c4205c64ba751554bba2c7acbdd162c2)
        ;

        
    
    
            circle_marker_08e2e537696e444eb2a22c1513d8a27b.bindTooltip(
                `<div>
                     RIO DO SUL: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0fa7f592133edb30de11dae30179c520 = L.circleMarker(
                [-23.7181, -46.8481],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_e1f7460485cfe1c18f01505767020741 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8bb48e24d2da9f91d5ca02e0e9c17893 = $(`<div id="html_8bb48e24d2da9f91d5ca02e0e9c17893" style="width: 100.0%; height: 100.0%;"><b>ITAPECERICA DA SERRA (SP)</b><br>1 registros</div>`)[0];
                popup_e1f7460485cfe1c18f01505767020741.setContent(html_8bb48e24d2da9f91d5ca02e0e9c17893);
            
        

        circle_marker_0fa7f592133edb30de11dae30179c520.bindPopup(popup_e1f7460485cfe1c18f01505767020741)
        ;

        
    
    
            circle_marker_0fa7f592133edb30de11dae30179c520.bindTooltip(
                `<div>
                     ITAPECERICA DA SERRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5f61540fa4d7ca8d1d27c152a5773c4b = L.circleMarker(
                [-23.3053, -45.9658],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_20563e0bb6ef09c301fab77004601d84 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f841c0c8e80118ee695ca4a82037d438 = $(`<div id="html_f841c0c8e80118ee695ca4a82037d438" style="width: 100.0%; height: 100.0%;"><b>JACAREI (SP)</b><br>2 registros</div>`)[0];
                popup_20563e0bb6ef09c301fab77004601d84.setContent(html_f841c0c8e80118ee695ca4a82037d438);
            
        

        circle_marker_5f61540fa4d7ca8d1d27c152a5773c4b.bindPopup(popup_20563e0bb6ef09c301fab77004601d84)
        ;

        
    
    
            circle_marker_5f61540fa4d7ca8d1d27c152a5773c4b.bindTooltip(
                `<div>
                     JACAREI: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f1f0943d3d893bde9121b8b30cd7e899 = L.circleMarker(
                [-22.9781, -49.8731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_bfe8e70642e1f3a7130a144f7bf6ee32 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a4c3c529b2111152deb0cb4a78cff16a = $(`<div id="html_a4c3c529b2111152deb0cb4a78cff16a" style="width: 100.0%; height: 100.0%;"><b>OURINHOS (SP)</b><br>2 registros</div>`)[0];
                popup_bfe8e70642e1f3a7130a144f7bf6ee32.setContent(html_a4c3c529b2111152deb0cb4a78cff16a);
            
        

        circle_marker_f1f0943d3d893bde9121b8b30cd7e899.bindPopup(popup_bfe8e70642e1f3a7130a144f7bf6ee32)
        ;

        
    
    
            circle_marker_f1f0943d3d893bde9121b8b30cd7e899.bindTooltip(
                `<div>
                     OURINHOS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3f77c3965c32451d107afe494f5d90a3 = L.circleMarker(
                [-22.1831, -47.3931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_56bbb7cbb075510dd77ce4bcd78019f2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_481d69ecf7b8162c9ca44fd7fdda347c = $(`<div id="html_481d69ecf7b8162c9ca44fd7fdda347c" style="width: 100.0%; height: 100.0%;"><b>LEME (SP)</b><br>1 registros</div>`)[0];
                popup_56bbb7cbb075510dd77ce4bcd78019f2.setContent(html_481d69ecf7b8162c9ca44fd7fdda347c);
            
        

        circle_marker_3f77c3965c32451d107afe494f5d90a3.bindPopup(popup_56bbb7cbb075510dd77ce4bcd78019f2)
        ;

        
    
    
            circle_marker_3f77c3965c32451d107afe494f5d90a3.bindTooltip(
                `<div>
                     LEME: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_769348c0349e5c6068842f674ae070e0 = L.circleMarker(
                [-20.4711, -45.9564],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_950dac17002c0ca728ff799be0743dcc = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9b473e47afa284ea90174c4822df7583 = $(`<div id="html_9b473e47afa284ea90174c4822df7583" style="width: 100.0%; height: 100.0%;"><b>PIUMHI (MG)</b><br>1 registros</div>`)[0];
                popup_950dac17002c0ca728ff799be0743dcc.setContent(html_9b473e47afa284ea90174c4822df7583);
            
        

        circle_marker_769348c0349e5c6068842f674ae070e0.bindPopup(popup_950dac17002c0ca728ff799be0743dcc)
        ;

        
    
    
            circle_marker_769348c0349e5c6068842f674ae070e0.bindTooltip(
                `<div>
                     PIUMHI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3fc63d4904fa86996208d1b101c96272 = L.circleMarker(
                [-23.3045, -51.1696],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_8f82fda7259545fd11155a92d49c89e4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cdd75ef54bd7a8d1c4a3f172108bd2d4 = $(`<div id="html_cdd75ef54bd7a8d1c4a3f172108bd2d4" style="width: 100.0%; height: 100.0%;"><b>LONDRINA (PR)</b><br>1 registros</div>`)[0];
                popup_8f82fda7259545fd11155a92d49c89e4.setContent(html_cdd75ef54bd7a8d1c4a3f172108bd2d4);
            
        

        circle_marker_3fc63d4904fa86996208d1b101c96272.bindPopup(popup_8f82fda7259545fd11155a92d49c89e4)
        ;

        
    
    
            circle_marker_3fc63d4904fa86996208d1b101c96272.bindTooltip(
                `<div>
                     LONDRINA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_fac6ed3cc5a19fc31628baeb1c957d70 = L.circleMarker(
                [-23.2281, -47.9531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_57d4e757f9bd486ffb8cacdd615200eb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_559891490645c0f744ab02841670a78e = $(`<div id="html_559891490645c0f744ab02841670a78e" style="width: 100.0%; height: 100.0%;"><b>CESARIO LANGE (SP)</b><br>1 registros</div>`)[0];
                popup_57d4e757f9bd486ffb8cacdd615200eb.setContent(html_559891490645c0f744ab02841670a78e);
            
        

        circle_marker_fac6ed3cc5a19fc31628baeb1c957d70.bindPopup(popup_57d4e757f9bd486ffb8cacdd615200eb)
        ;

        
    
    
            circle_marker_fac6ed3cc5a19fc31628baeb1c957d70.bindTooltip(
                `<div>
                     CESARIO LANGE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b6e51103e5820565a19dd0b8d34a0f38 = L.circleMarker(
                [-22.3711, -41.7869],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_7e86da90c4a9e183450a8ad87018da3c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ef6145ce828d403bf96dbf2ed9ecea1f = $(`<div id="html_ef6145ce828d403bf96dbf2ed9ecea1f" style="width: 100.0%; height: 100.0%;"><b>MACAE (RJ)</b><br>2 registros</div>`)[0];
                popup_7e86da90c4a9e183450a8ad87018da3c.setContent(html_ef6145ce828d403bf96dbf2ed9ecea1f);
            
        

        circle_marker_b6e51103e5820565a19dd0b8d34a0f38.bindPopup(popup_7e86da90c4a9e183450a8ad87018da3c)
        ;

        
    
    
            circle_marker_b6e51103e5820565a19dd0b8d34a0f38.bindTooltip(
                `<div>
                     MACAE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_72e80d60e8cf7bd38d08a78e6e61e329 = L.circleMarker(
                [-23.0264, -45.5556],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_b35a797f230997ba701bb1683a4dc754 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_14243839241c90de816962197d73bedd = $(`<div id="html_14243839241c90de816962197d73bedd" style="width: 100.0%; height: 100.0%;"><b>TAUBATE (SP)</b><br>2 registros</div>`)[0];
                popup_b35a797f230997ba701bb1683a4dc754.setContent(html_14243839241c90de816962197d73bedd);
            
        

        circle_marker_72e80d60e8cf7bd38d08a78e6e61e329.bindPopup(popup_b35a797f230997ba701bb1683a4dc754)
        ;

        
    
    
            circle_marker_72e80d60e8cf7bd38d08a78e6e61e329.bindTooltip(
                `<div>
                     TAUBATE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c13b358d49eeee8813b38def53726c06 = L.circleMarker(
                [-22.3208, -49.0608],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_37112c319da75672f7ab0e305ccdc811 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f17f723781ec08cb8ac007ebca02dbbd = $(`<div id="html_f17f723781ec08cb8ac007ebca02dbbd" style="width: 100.0%; height: 100.0%;"><b>BAURU (SP)</b><br>3 registros</div>`)[0];
                popup_37112c319da75672f7ab0e305ccdc811.setContent(html_f17f723781ec08cb8ac007ebca02dbbd);
            
        

        circle_marker_c13b358d49eeee8813b38def53726c06.bindPopup(popup_37112c319da75672f7ab0e305ccdc811)
        ;

        
    
    
            circle_marker_c13b358d49eeee8813b38def53726c06.bindTooltip(
                `<div>
                     BAURU: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7a4a19bb87d2ff4ac31aa3ef1e4fcab6 = L.circleMarker(
                [-22.5731, -44.9631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_53d4850c4cd67feef2cfb177e25ffcd9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5ec0d34b61650d92210b297ae14bcb4e = $(`<div id="html_5ec0d34b61650d92210b297ae14bcb4e" style="width: 100.0%; height: 100.0%;"><b>CRUZEIRO (SP)</b><br>1 registros</div>`)[0];
                popup_53d4850c4cd67feef2cfb177e25ffcd9.setContent(html_5ec0d34b61650d92210b297ae14bcb4e);
            
        

        circle_marker_7a4a19bb87d2ff4ac31aa3ef1e4fcab6.bindPopup(popup_53d4850c4cd67feef2cfb177e25ffcd9)
        ;

        
    
    
            circle_marker_7a4a19bb87d2ff4ac31aa3ef1e4fcab6.bindTooltip(
                `<div>
                     CRUZEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_be7d2f032c41c6345ed05a4baff66f0d = L.circleMarker(
                [-18.9113, -48.2622],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_4622f7cd1dee8826a34f373b603c7221 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d55db91330f7417bfe7159a6094697a6 = $(`<div id="html_d55db91330f7417bfe7159a6094697a6" style="width: 100.0%; height: 100.0%;"><b>UBERLANDIA (MG)</b><br>1 registros</div>`)[0];
                popup_4622f7cd1dee8826a34f373b603c7221.setContent(html_d55db91330f7417bfe7159a6094697a6);
            
        

        circle_marker_be7d2f032c41c6345ed05a4baff66f0d.bindPopup(popup_4622f7cd1dee8826a34f373b603c7221)
        ;

        
    
    
            circle_marker_be7d2f032c41c6345ed05a4baff66f0d.bindTooltip(
                `<div>
                     UBERLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_190dc10f8f40b7bdddcc64e6436271ee = L.circleMarker(
                [-23.8531, -46.1381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_6996a4dbe28fcd41ec970e8da59acafd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d4117724ded453fcdbf8052a89641650 = $(`<div id="html_d4117724ded453fcdbf8052a89641650" style="width: 100.0%; height: 100.0%;"><b>BERTIOGA (SP)</b><br>1 registros</div>`)[0];
                popup_6996a4dbe28fcd41ec970e8da59acafd.setContent(html_d4117724ded453fcdbf8052a89641650);
            
        

        circle_marker_190dc10f8f40b7bdddcc64e6436271ee.bindPopup(popup_6996a4dbe28fcd41ec970e8da59acafd)
        ;

        
    
    
            circle_marker_190dc10f8f40b7bdddcc64e6436271ee.bindTooltip(
                `<div>
                     BERTIOGA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_89500f81ce180c5d6fc42df094dd7f1b = L.circleMarker(
                [-18.9431, -46.9931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_65d4ad5d942a174b05807863d6b7705c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e16b3c3ca7c3e1706979288116eff24f = $(`<div id="html_e16b3c3ca7c3e1706979288116eff24f" style="width: 100.0%; height: 100.0%;"><b>PATROCINIO (MG)</b><br>1 registros</div>`)[0];
                popup_65d4ad5d942a174b05807863d6b7705c.setContent(html_e16b3c3ca7c3e1706979288116eff24f);
            
        

        circle_marker_89500f81ce180c5d6fc42df094dd7f1b.bindPopup(popup_65d4ad5d942a174b05807863d6b7705c)
        ;

        
    
    
            circle_marker_89500f81ce180c5d6fc42df094dd7f1b.bindTooltip(
                `<div>
                     PATROCINIO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_67046564b0791ac6c927e6843eedf3be = L.circleMarker(
                [-23.1531, -47.0631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c24016191249729bf439f4144b421f5c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3eaa1d412cb8238290898f0cafa65d2d = $(`<div id="html_3eaa1d412cb8238290898f0cafa65d2d" style="width: 100.0%; height: 100.0%;"><b>ITUPEVA (SP)</b><br>1 registros</div>`)[0];
                popup_c24016191249729bf439f4144b421f5c.setContent(html_3eaa1d412cb8238290898f0cafa65d2d);
            
        

        circle_marker_67046564b0791ac6c927e6843eedf3be.bindPopup(popup_c24016191249729bf439f4144b421f5c)
        ;

        
    
    
            circle_marker_67046564b0791ac6c927e6843eedf3be.bindTooltip(
                `<div>
                     ITUPEVA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_efe2188f63f21485f8ef2eec744d38cc = L.circleMarker(
                [-23.0881, -46.9431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_810cc7cc94ab10a37b2832fe065bd64c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c9f290efc7c9c8bd21187c6cf68aa810 = $(`<div id="html_c9f290efc7c9c8bd21187c6cf68aa810" style="width: 100.0%; height: 100.0%;"><b>LOUVEIRA (SP)</b><br>1 registros</div>`)[0];
                popup_810cc7cc94ab10a37b2832fe065bd64c.setContent(html_c9f290efc7c9c8bd21187c6cf68aa810);
            
        

        circle_marker_efe2188f63f21485f8ef2eec744d38cc.bindPopup(popup_810cc7cc94ab10a37b2832fe065bd64c)
        ;

        
    
    
            circle_marker_efe2188f63f21485f8ef2eec744d38cc.bindTooltip(
                `<div>
                     LOUVEIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c6d538d8a63ea180bd589a14bd7ba8cb = L.circleMarker(
                [-23.7781, -45.3581],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_f6df5c51cc7b707cfde2382d10a56391 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_66194c194d7c865bb6ff69bebb7b30f5 = $(`<div id="html_66194c194d7c865bb6ff69bebb7b30f5" style="width: 100.0%; height: 100.0%;"><b>ILHABELA (SP)</b><br>1 registros</div>`)[0];
                popup_f6df5c51cc7b707cfde2382d10a56391.setContent(html_66194c194d7c865bb6ff69bebb7b30f5);
            
        

        circle_marker_c6d538d8a63ea180bd589a14bd7ba8cb.bindPopup(popup_f6df5c51cc7b707cfde2382d10a56391)
        ;

        
    
    
            circle_marker_c6d538d8a63ea180bd589a14bd7ba8cb.bindTooltip(
                `<div>
                     ILHABELA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_02437a19893bc6595953a392a92de423 = L.circleMarker(
                [-23.3631, -46.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_725c26c2ead0fd3a95485f44fb4777cd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ecd7c81bd4a985e3c680ac24fc069a07 = $(`<div id="html_ecd7c81bd4a985e3c680ac24fc069a07" style="width: 100.0%; height: 100.0%;"><b>CAIEIRAS (SP)</b><br>1 registros</div>`)[0];
                popup_725c26c2ead0fd3a95485f44fb4777cd.setContent(html_ecd7c81bd4a985e3c680ac24fc069a07);
            
        

        circle_marker_02437a19893bc6595953a392a92de423.bindPopup(popup_725c26c2ead0fd3a95485f44fb4777cd)
        ;

        
    
    
            circle_marker_02437a19893bc6595953a392a92de423.bindTooltip(
                `<div>
                     CAIEIRAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a3901dac80c960f80c1782ab703c4623 = L.circleMarker(
                [-21.2731, -47.3031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_f32277515841d812fc72fd4890cfc67d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e45fde972aedbb231d2fa3fbcc57dbf2 = $(`<div id="html_e45fde972aedbb231d2fa3fbcc57dbf2" style="width: 100.0%; height: 100.0%;"><b>CAJURU (SP)</b><br>1 registros</div>`)[0];
                popup_f32277515841d812fc72fd4890cfc67d.setContent(html_e45fde972aedbb231d2fa3fbcc57dbf2);
            
        

        circle_marker_a3901dac80c960f80c1782ab703c4623.bindPopup(popup_f32277515841d812fc72fd4890cfc67d)
        ;

        
    
    
            circle_marker_a3901dac80c960f80c1782ab703c4623.bindTooltip(
                `<div>
                     CAJURU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_dacd8d49e75c87f3abcc08cbd4bac3f6 = L.circleMarker(
                [-22.8481, -45.2331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_679e227e294078227c9e8a6fed3983b2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_bfb926d079a0e5b0f716bba6a0495a44 = $(`<div id="html_bfb926d079a0e5b0f716bba6a0495a44" style="width: 100.0%; height: 100.0%;"><b>APARECIDA (SP)</b><br>1 registros</div>`)[0];
                popup_679e227e294078227c9e8a6fed3983b2.setContent(html_bfb926d079a0e5b0f716bba6a0495a44);
            
        

        circle_marker_dacd8d49e75c87f3abcc08cbd4bac3f6.bindPopup(popup_679e227e294078227c9e8a6fed3983b2)
        ;

        
    
    
            circle_marker_dacd8d49e75c87f3abcc08cbd4bac3f6.bindTooltip(
                `<div>
                     APARECIDA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_bd0f2233a04d842956f78007d28c1ef6 = L.circleMarker(
                [-26.7431, -49.1781],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_1813134787310905f45a8617d9fd48f8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5ad61155d9ea9d0c2d95abc4f9083a04 = $(`<div id="html_5ad61155d9ea9d0c2d95abc4f9083a04" style="width: 100.0%; height: 100.0%;"><b>POMERODE (SC)</b><br>1 registros</div>`)[0];
                popup_1813134787310905f45a8617d9fd48f8.setContent(html_5ad61155d9ea9d0c2d95abc4f9083a04);
            
        

        circle_marker_bd0f2233a04d842956f78007d28c1ef6.bindPopup(popup_1813134787310905f45a8617d9fd48f8)
        ;

        
    
    
            circle_marker_bd0f2233a04d842956f78007d28c1ef6.bindTooltip(
                `<div>
                     POMERODE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9d05bc994ad5d7c9732112c7ea7792bd = L.circleMarker(
                [-27.1581, -48.5531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_93b8e52f33d6f4580f748d46e07ba61d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_187594ffe56c55521a4539eb1bcaee3a = $(`<div id="html_187594ffe56c55521a4539eb1bcaee3a" style="width: 100.0%; height: 100.0%;"><b>PORTO BELO (SC)</b><br>1 registros</div>`)[0];
                popup_93b8e52f33d6f4580f748d46e07ba61d.setContent(html_187594ffe56c55521a4539eb1bcaee3a);
            
        

        circle_marker_9d05bc994ad5d7c9732112c7ea7792bd.bindPopup(popup_93b8e52f33d6f4580f748d46e07ba61d)
        ;

        
    
    
            circle_marker_9d05bc994ad5d7c9732112c7ea7792bd.bindTooltip(
                `<div>
                     PORTO BELO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b1450aaf44bd47a43a7a5b49997d38ed = L.circleMarker(
                [-23.5489, -46.9342],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_30b0ad8e03fd527a29a0c3d29d20c8e5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_90ee24f320c558ae0a255088ea83ef5d = $(`<div id="html_90ee24f320c558ae0a255088ea83ef5d" style="width: 100.0%; height: 100.0%;"><b>ITAPEVI (SP)</b><br>1 registros</div>`)[0];
                popup_30b0ad8e03fd527a29a0c3d29d20c8e5.setContent(html_90ee24f320c558ae0a255088ea83ef5d);
            
        

        circle_marker_b1450aaf44bd47a43a7a5b49997d38ed.bindPopup(popup_30b0ad8e03fd527a29a0c3d29d20c8e5)
        ;

        
    
    
            circle_marker_b1450aaf44bd47a43a7a5b49997d38ed.bindTooltip(
                `<div>
                     ITAPEVI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ffd3cbcccdbc6e53a2e38abfe070c5f4 = L.circleMarker(
                [-22.7253, -47.6492],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a4771e0cf9995482de0be5fbbe5430c5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_30f0b1b443a850f4228a74dbc50fc935 = $(`<div id="html_30f0b1b443a850f4228a74dbc50fc935" style="width: 100.0%; height: 100.0%;"><b>PIRACICABA (SP)</b><br>1 registros</div>`)[0];
                popup_a4771e0cf9995482de0be5fbbe5430c5.setContent(html_30f0b1b443a850f4228a74dbc50fc935);
            
        

        circle_marker_ffd3cbcccdbc6e53a2e38abfe070c5f4.bindPopup(popup_a4771e0cf9995482de0be5fbbe5430c5)
        ;

        
    
    
            circle_marker_ffd3cbcccdbc6e53a2e38abfe070c5f4.bindTooltip(
                `<div>
                     PIRACICABA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_51fea6171948b2dec9f7fb37a6e7c54e = L.circleMarker(
                [-22.8581, -47.2181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_3aa0361175ebdeca928bc06ece554244 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_59c30bf7e31c7bf79697845e054b2474 = $(`<div id="html_59c30bf7e31c7bf79697845e054b2474" style="width: 100.0%; height: 100.0%;"><b>HORTOLANDIA (SP)</b><br>1 registros</div>`)[0];
                popup_3aa0361175ebdeca928bc06ece554244.setContent(html_59c30bf7e31c7bf79697845e054b2474);
            
        

        circle_marker_51fea6171948b2dec9f7fb37a6e7c54e.bindPopup(popup_3aa0361175ebdeca928bc06ece554244)
        ;

        
    
    
            circle_marker_51fea6171948b2dec9f7fb37a6e7c54e.bindTooltip(
                `<div>
                     HORTOLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5ee45d5e3742e21dec809e8c8dadd2d6 = L.circleMarker(
                [-22.3681, -46.9431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_7d94904e14e0197cceb85e0cbcded318 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_11b03fe441b304aa227302333225d6e7 = $(`<div id="html_11b03fe441b304aa227302333225d6e7" style="width: 100.0%; height: 100.0%;"><b>MOGI GUACU (SP)</b><br>1 registros</div>`)[0];
                popup_7d94904e14e0197cceb85e0cbcded318.setContent(html_11b03fe441b304aa227302333225d6e7);
            
        

        circle_marker_5ee45d5e3742e21dec809e8c8dadd2d6.bindPopup(popup_7d94904e14e0197cceb85e0cbcded318)
        ;

        
    
    
            circle_marker_5ee45d5e3742e21dec809e8c8dadd2d6.bindTooltip(
                `<div>
                     MOGI GUACU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5c99db438ce532568bb235054f65c022 = L.circleMarker(
                [-21.6731, -49.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_072b2ecf233b1abc3d2e99a43dbaff3c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_940882efaa5620a1642fd0e675f54f86 = $(`<div id="html_940882efaa5620a1642fd0e675f54f86" style="width: 100.0%; height: 100.0%;"><b>LINS (SP)</b><br>1 registros</div>`)[0];
                popup_072b2ecf233b1abc3d2e99a43dbaff3c.setContent(html_940882efaa5620a1642fd0e675f54f86);
            
        

        circle_marker_5c99db438ce532568bb235054f65c022.bindPopup(popup_072b2ecf233b1abc3d2e99a43dbaff3c)
        ;

        
    
    
            circle_marker_5c99db438ce532568bb235054f65c022.bindTooltip(
                `<div>
                     LINS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f484e2dad3288e084de4910e69696feb = L.circleMarker(
                [-23.1681, -47.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_ae9e1c44535357762e4fab8c07506c1a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cacd29f5e5d2974f85668711d8e66f3d = $(`<div id="html_cacd29f5e5d2974f85668711d8e66f3d" style="width: 100.0%; height: 100.0%;"><b>CERQUILHO (SP)</b><br>1 registros</div>`)[0];
                popup_ae9e1c44535357762e4fab8c07506c1a.setContent(html_cacd29f5e5d2974f85668711d8e66f3d);
            
        

        circle_marker_f484e2dad3288e084de4910e69696feb.bindPopup(popup_ae9e1c44535357762e4fab8c07506c1a)
        ;

        
    
    
            circle_marker_f484e2dad3288e084de4910e69696feb.bindTooltip(
                `<div>
                     CERQUILHO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0b94efa5d2928309b829d73157139da5 = L.circleMarker(
                [-22.9731, -46.9981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_3b524db9b08d0eae125de6aa9841a3e4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_11ad75a111cc15e859819840ee01fe9b = $(`<div id="html_11ad75a111cc15e859819840ee01fe9b" style="width: 100.0%; height: 100.0%;"><b>VALINHOS (SP)</b><br>1 registros</div>`)[0];
                popup_3b524db9b08d0eae125de6aa9841a3e4.setContent(html_11ad75a111cc15e859819840ee01fe9b);
            
        

        circle_marker_0b94efa5d2928309b829d73157139da5.bindPopup(popup_3b524db9b08d0eae125de6aa9841a3e4)
        ;

        
    
    
            circle_marker_0b94efa5d2928309b829d73157139da5.bindTooltip(
                `<div>
                     VALINHOS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_71a70e104a7285723940e1a30ba4dd03 = L.circleMarker(
                [-21.2089, -50.4331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_b4f10001305e42ad3c9bfd8b843d24a2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_361acd93ec9db7348f9c881c3cb300c9 = $(`<div id="html_361acd93ec9db7348f9c881c3cb300c9" style="width: 100.0%; height: 100.0%;"><b>ARACATUBA (SP)</b><br>2 registros</div>`)[0];
                popup_b4f10001305e42ad3c9bfd8b843d24a2.setContent(html_361acd93ec9db7348f9c881c3cb300c9);
            
        

        circle_marker_71a70e104a7285723940e1a30ba4dd03.bindPopup(popup_b4f10001305e42ad3c9bfd8b843d24a2)
        ;

        
    
    
            circle_marker_71a70e104a7285723940e1a30ba4dd03.bindTooltip(
                `<div>
                     ARACATUBA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_878e002b232c2a15c5859dfa5e9e0ec9 = L.circleMarker(
                [-21.7642, -41.3297],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_980e2672e91d525b0b576a76cd99bc82 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a3ecc5c0d978222a836ed79a420746ab = $(`<div id="html_a3ecc5c0d978222a836ed79a420746ab" style="width: 100.0%; height: 100.0%;"><b>CAMPOS DOS GOYTACAZES (RJ)</b><br>1 registros</div>`)[0];
                popup_980e2672e91d525b0b576a76cd99bc82.setContent(html_a3ecc5c0d978222a836ed79a420746ab);
            
        

        circle_marker_878e002b232c2a15c5859dfa5e9e0ec9.bindPopup(popup_980e2672e91d525b0b576a76cd99bc82)
        ;

        
    
    
            circle_marker_878e002b232c2a15c5859dfa5e9e0ec9.bindTooltip(
                `<div>
                     CAMPOS DOS GOYTACAZES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1cbae62e7f16215fd28f63cc7365e0b5 = L.circleMarker(
                [-21.6231, -49.0731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_9d3af3c33268ddeb20c72402f05d385b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_023ae544fd7c2980d79cc5c4111686cf = $(`<div id="html_023ae544fd7c2980d79cc5c4111686cf" style="width: 100.0%; height: 100.0%;"><b>BORBOREMA (SP)</b><br>1 registros</div>`)[0];
                popup_9d3af3c33268ddeb20c72402f05d385b.setContent(html_023ae544fd7c2980d79cc5c4111686cf);
            
        

        circle_marker_1cbae62e7f16215fd28f63cc7365e0b5.bindPopup(popup_9d3af3c33268ddeb20c72402f05d385b)
        ;

        
    
    
            circle_marker_1cbae62e7f16215fd28f63cc7365e0b5.bindTooltip(
                `<div>
                     BORBOREMA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6e9d751d8f88c685d50d79fac352c738 = L.circleMarker(
                [-23.6861, -46.6228],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_5be55c42c51979123db318b43afb6473 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c2907500624e20a8e559a0ec977959cf = $(`<div id="html_c2907500624e20a8e559a0ec977959cf" style="width: 100.0%; height: 100.0%;"><b>DIADEMA (SP)</b><br>2 registros</div>`)[0];
                popup_5be55c42c51979123db318b43afb6473.setContent(html_c2907500624e20a8e559a0ec977959cf);
            
        

        circle_marker_6e9d751d8f88c685d50d79fac352c738.bindPopup(popup_5be55c42c51979123db318b43afb6473)
        ;

        
    
    
            circle_marker_6e9d751d8f88c685d50d79fac352c738.bindTooltip(
                `<div>
                     DIADEMA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_38cc0d8a1d252f64cb7bc1b16a608c37 = L.circleMarker(
                [-25.3842, -51.4617],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_1488ebf8b27b20293e8dd8bdfc9d65c9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_986cdc23626b3ede9373594f27db7ecb = $(`<div id="html_986cdc23626b3ede9373594f27db7ecb" style="width: 100.0%; height: 100.0%;"><b>GUARAPUAVA (PR)</b><br>4 registros</div>`)[0];
                popup_1488ebf8b27b20293e8dd8bdfc9d65c9.setContent(html_986cdc23626b3ede9373594f27db7ecb);
            
        

        circle_marker_38cc0d8a1d252f64cb7bc1b16a608c37.bindPopup(popup_1488ebf8b27b20293e8dd8bdfc9d65c9)
        ;

        
    
    
            circle_marker_38cc0d8a1d252f64cb7bc1b16a608c37.bindTooltip(
                `<div>
                     GUARAPUAVA: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_388d531b1d0dca5d39d3c27563663139 = L.circleMarker(
                [-22.7611, -47.1544],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_5f1a4f3c4b580636d93b2f2e35f133df = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b788d4b1dbb0352d74bec2761e150077 = $(`<div id="html_b788d4b1dbb0352d74bec2761e150077" style="width: 100.0%; height: 100.0%;"><b>PAULINIA (SP)</b><br>5 registros</div>`)[0];
                popup_5f1a4f3c4b580636d93b2f2e35f133df.setContent(html_b788d4b1dbb0352d74bec2761e150077);
            
        

        circle_marker_388d531b1d0dca5d39d3c27563663139.bindPopup(popup_5f1a4f3c4b580636d93b2f2e35f133df)
        ;

        
    
    
            circle_marker_388d531b1d0dca5d39d3c27563663139.bindTooltip(
                `<div>
                     PAULINIA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2cbbfcf03c7c376f99cf44cf1bca4d2f = L.circleMarker(
                [-21.9681, -46.7981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_81e4eedfc90392dcc22d4b01ce5d3a47 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1e0701b979bd4096190996c37f7da08e = $(`<div id="html_1e0701b979bd4096190996c37f7da08e" style="width: 100.0%; height: 100.0%;"><b>SAO JOAO DA BOA VISTA (SP)</b><br>1 registros</div>`)[0];
                popup_81e4eedfc90392dcc22d4b01ce5d3a47.setContent(html_1e0701b979bd4096190996c37f7da08e);
            
        

        circle_marker_2cbbfcf03c7c376f99cf44cf1bca4d2f.bindPopup(popup_81e4eedfc90392dcc22d4b01ce5d3a47)
        ;

        
    
    
            circle_marker_2cbbfcf03c7c376f99cf44cf1bca4d2f.bindTooltip(
                `<div>
                     SAO JOAO DA BOA VISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d27b03d6ef1b96dd0a8c366eda91fb6b = L.circleMarker(
                [-7.9406, -34.8728],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c612555098345f0f0e965b9aec814956 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_67e7ed377633432a15e578c975d8473f = $(`<div id="html_67e7ed377633432a15e578c975d8473f" style="width: 100.0%; height: 100.0%;"><b>PAULISTA (PE)</b><br>1 registros</div>`)[0];
                popup_c612555098345f0f0e965b9aec814956.setContent(html_67e7ed377633432a15e578c975d8473f);
            
        

        circle_marker_d27b03d6ef1b96dd0a8c366eda91fb6b.bindPopup(popup_c612555098345f0f0e965b9aec814956)
        ;

        
    
    
            circle_marker_d27b03d6ef1b96dd0a8c366eda91fb6b.bindTooltip(
                `<div>
                     PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a0e92f004700c83569e7aa855a4c9ea4 = L.circleMarker(
                [-20.4697, -54.6201],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkblue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_00a885d650cab9268406ca42970a7b43 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ca29708b1578d60f54ed2c7b8e4134ed = $(`<div id="html_ca29708b1578d60f54ed2c7b8e4134ed" style="width: 100.0%; height: 100.0%;"><b>CAMPO GRANDE (MS)</b><br>2 registros</div>`)[0];
                popup_00a885d650cab9268406ca42970a7b43.setContent(html_ca29708b1578d60f54ed2c7b8e4134ed);
            
        

        circle_marker_a0e92f004700c83569e7aa855a4c9ea4.bindPopup(popup_00a885d650cab9268406ca42970a7b43)
        ;

        
    
    
            circle_marker_a0e92f004700c83569e7aa855a4c9ea4.bindTooltip(
                `<div>
                     CAMPO GRANDE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f2e13badd77f5f11370d5b1f8c0804b5 = L.circleMarker(
                [-8.112, -35.0145],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a71090131ccddc928746135febb533a2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_152e09cb2835609a8b18b8becec66afd = $(`<div id="html_152e09cb2835609a8b18b8becec66afd" style="width: 100.0%; height: 100.0%;"><b>JABOATAO DOS GUARARAPES (PE)</b><br>1 registros</div>`)[0];
                popup_a71090131ccddc928746135febb533a2.setContent(html_152e09cb2835609a8b18b8becec66afd);
            
        

        circle_marker_f2e13badd77f5f11370d5b1f8c0804b5.bindPopup(popup_a71090131ccddc928746135febb533a2)
        ;

        
    
    
            circle_marker_f2e13badd77f5f11370d5b1f8c0804b5.bindTooltip(
                `<div>
                     JABOATAO DOS GUARARAPES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2886f498cd9126b8a25d4bd72db17cb8 = L.circleMarker(
                [-8.0476, -34.877],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c320236d1ecbf4ae207554e9ef04e7bf = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_325ef987644c28e68b51df9d54d97f3f = $(`<div id="html_325ef987644c28e68b51df9d54d97f3f" style="width: 100.0%; height: 100.0%;"><b>RECIFE (PE)</b><br>3 registros</div>`)[0];
                popup_c320236d1ecbf4ae207554e9ef04e7bf.setContent(html_325ef987644c28e68b51df9d54d97f3f);
            
        

        circle_marker_2886f498cd9126b8a25d4bd72db17cb8.bindPopup(popup_c320236d1ecbf4ae207554e9ef04e7bf)
        ;

        
    
    
            circle_marker_2886f498cd9126b8a25d4bd72db17cb8.bindTooltip(
                `<div>
                     RECIFE: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8511e16813f80516b4417515e6c34deb = L.circleMarker(
                [-15.6014, -56.0979],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_ac4fb1ef3ad35a853b2cb5ba109c042d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5022222607f51c8b97436a30ad192a34 = $(`<div id="html_5022222607f51c8b97436a30ad192a34" style="width: 100.0%; height: 100.0%;"><b>CUIABA (MT)</b><br>2 registros</div>`)[0];
                popup_ac4fb1ef3ad35a853b2cb5ba109c042d.setContent(html_5022222607f51c8b97436a30ad192a34);
            
        

        circle_marker_8511e16813f80516b4417515e6c34deb.bindPopup(popup_ac4fb1ef3ad35a853b2cb5ba109c042d)
        ;

        
    
    
            circle_marker_8511e16813f80516b4417515e6c34deb.bindTooltip(
                `<div>
                     CUIABA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d37734f60b01f1dc0a8a48a9cd574736 = L.circleMarker(
                [-19.3931, -54.5631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkblue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_7c24d3ca977c530a62249d3373abfac3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_71c452133c53a90137c53ab7a7ce89a2 = $(`<div id="html_71c452133c53a90137c53ab7a7ce89a2" style="width: 100.0%; height: 100.0%;"><b>SAO GABRIEL DO OESTE (MS)</b><br>1 registros</div>`)[0];
                popup_7c24d3ca977c530a62249d3373abfac3.setContent(html_71c452133c53a90137c53ab7a7ce89a2);
            
        

        circle_marker_d37734f60b01f1dc0a8a48a9cd574736.bindPopup(popup_7c24d3ca977c530a62249d3373abfac3)
        ;

        
    
    
            circle_marker_d37734f60b01f1dc0a8a48a9cd574736.bindTooltip(
                `<div>
                     SAO GABRIEL DO OESTE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_70ba0b69fddae0fa0406c249b8b25bde = L.circleMarker(
                [-22.5264, -41.9456],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_666c631e0ca18efe56f0d82b1c1beab3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_225627819975dbcbad9194b8a419168a = $(`<div id="html_225627819975dbcbad9194b8a419168a" style="width: 100.0%; height: 100.0%;"><b>RIO DAS OSTRAS (RJ)</b><br>1 registros</div>`)[0];
                popup_666c631e0ca18efe56f0d82b1c1beab3.setContent(html_225627819975dbcbad9194b8a419168a);
            
        

        circle_marker_70ba0b69fddae0fa0406c249b8b25bde.bindPopup(popup_666c631e0ca18efe56f0d82b1c1beab3)
        ;

        
    
    
            circle_marker_70ba0b69fddae0fa0406c249b8b25bde.bindTooltip(
                `<div>
                     RIO DAS OSTRAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_33fadd5ac614956dfaf2280249c8dce4 = L.circleMarker(
                [-7.1195, -34.845],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "lightblue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_f5c736281e2c80ab6ed196d43ae0bdb3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5b0dc5ba3c0f0ee498910d9c835fed3e = $(`<div id="html_5b0dc5ba3c0f0ee498910d9c835fed3e" style="width: 100.0%; height: 100.0%;"><b>JOAO PESSOA (PB)</b><br>1 registros</div>`)[0];
                popup_f5c736281e2c80ab6ed196d43ae0bdb3.setContent(html_5b0dc5ba3c0f0ee498910d9c835fed3e);
            
        

        circle_marker_33fadd5ac614956dfaf2280249c8dce4.bindPopup(popup_f5c736281e2c80ab6ed196d43ae0bdb3)
        ;

        
    
    
            circle_marker_33fadd5ac614956dfaf2280249c8dce4.bindTooltip(
                `<div>
                     JOAO PESSOA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_80fc5adcf27db34f63119484a9bc084e = L.circleMarker(
                [-26.9194, -49.0661],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_fb807048d6ffb728b0ba4ca112b0d2dc = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_47b68f31d896486f4fbb5c16c190b44a = $(`<div id="html_47b68f31d896486f4fbb5c16c190b44a" style="width: 100.0%; height: 100.0%;"><b>BLUMENAU (SC)</b><br>1 registros</div>`)[0];
                popup_fb807048d6ffb728b0ba4ca112b0d2dc.setContent(html_47b68f31d896486f4fbb5c16c190b44a);
            
        

        circle_marker_80fc5adcf27db34f63119484a9bc084e.bindPopup(popup_fb807048d6ffb728b0ba4ca112b0d2dc)
        ;

        
    
    
            circle_marker_80fc5adcf27db34f63119484a9bc084e.bindTooltip(
                `<div>
                     BLUMENAU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_56c31dac0dfdd128eb6b60c7218d8d17 = L.circleMarker(
                [-27.0964, -52.6181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_b5343fd17ffd6703ad8e09d50b9320ba = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e6c661df54c07b8f4c797b837d1b28ea = $(`<div id="html_e6c661df54c07b8f4c797b837d1b28ea" style="width: 100.0%; height: 100.0%;"><b>CHAPECO (SC)</b><br>1 registros</div>`)[0];
                popup_b5343fd17ffd6703ad8e09d50b9320ba.setContent(html_e6c661df54c07b8f4c797b837d1b28ea);
            
        

        circle_marker_56c31dac0dfdd128eb6b60c7218d8d17.bindPopup(popup_b5343fd17ffd6703ad8e09d50b9320ba)
        ;

        
    
    
            circle_marker_56c31dac0dfdd128eb6b60c7218d8d17.bindTooltip(
                `<div>
                     CHAPECO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a5def614c86a554f357ece5705854270 = L.circleMarker(
                [-26.8981, -49.2331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_eed56cb3cb3706878975b335f2e4aef2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_54a507429912ffa73e1dc7c6bf0d2981 = $(`<div id="html_54a507429912ffa73e1dc7c6bf0d2981" style="width: 100.0%; height: 100.0%;"><b>INDAIAL (SC)</b><br>1 registros</div>`)[0];
                popup_eed56cb3cb3706878975b335f2e4aef2.setContent(html_54a507429912ffa73e1dc7c6bf0d2981);
            
        

        circle_marker_a5def614c86a554f357ece5705854270.bindPopup(popup_eed56cb3cb3706878975b335f2e4aef2)
        ;

        
    
    
            circle_marker_a5def614c86a554f357ece5705854270.bindTooltip(
                `<div>
                     INDAIAL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_228838acc81847752045c665064a1a64 = L.circleMarker(
                [-26.2431, -48.6381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_24d66873101ec9f63dd939d9bef7fd01 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_27ce9a03c3d61d77a11121a520507976 = $(`<div id="html_27ce9a03c3d61d77a11121a520507976" style="width: 100.0%; height: 100.0%;"><b>SAO FRANCISCO DO SUL (SC)</b><br>1 registros</div>`)[0];
                popup_24d66873101ec9f63dd939d9bef7fd01.setContent(html_27ce9a03c3d61d77a11121a520507976);
            
        

        circle_marker_228838acc81847752045c665064a1a64.bindPopup(popup_24d66873101ec9f63dd939d9bef7fd01)
        ;

        
    
    
            circle_marker_228838acc81847752045c665064a1a64.bindTooltip(
                `<div>
                     SAO FRANCISCO DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_098a8a89c5a40f55df0db0e12cbc39f1 = L.circleMarker(
                [-26.3731, -48.7231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_8ec10e482ac145128084ee51a94678a5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7fd490d2c804d0fcb95a52015f4979cc = $(`<div id="html_7fd490d2c804d0fcb95a52015f4979cc" style="width: 100.0%; height: 100.0%;"><b>ARAQUARI (SC)</b><br>1 registros</div>`)[0];
                popup_8ec10e482ac145128084ee51a94678a5.setContent(html_7fd490d2c804d0fcb95a52015f4979cc);
            
        

        circle_marker_098a8a89c5a40f55df0db0e12cbc39f1.bindPopup(popup_8ec10e482ac145128084ee51a94678a5)
        ;

        
    
    
            circle_marker_098a8a89c5a40f55df0db0e12cbc39f1.bindTooltip(
                `<div>
                     ARAQUARI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8ae7bd0bd3e9ef567bc0311d86f7a609 = L.circleMarker(
                [-15.3081, -49.5981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a53a60ab9761dd5c66f982eba3ef50a0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_401afdb3e841c606d192172dc1956418 = $(`<div id="html_401afdb3e841c606d192172dc1956418" style="width: 100.0%; height: 100.0%;"><b>CERES (GO)</b><br>1 registros</div>`)[0];
                popup_a53a60ab9761dd5c66f982eba3ef50a0.setContent(html_401afdb3e841c606d192172dc1956418);
            
        

        circle_marker_8ae7bd0bd3e9ef567bc0311d86f7a609.bindPopup(popup_a53a60ab9761dd5c66f982eba3ef50a0)
        ;

        
    
    
            circle_marker_8ae7bd0bd3e9ef567bc0311d86f7a609.bindTooltip(
                `<div>
                     CERES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9465f2f9e255f95e50bbe893deae8103 = L.circleMarker(
                [-8.0089, -34.8553],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_bf8452448b9eda97b1f8fa7f2e533d34 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c77a9ce13bf1d984408f8f46a5d3a5b1 = $(`<div id="html_c77a9ce13bf1d984408f8f46a5d3a5b1" style="width: 100.0%; height: 100.0%;"><b>OLINDA (PE)</b><br>1 registros</div>`)[0];
                popup_bf8452448b9eda97b1f8fa7f2e533d34.setContent(html_c77a9ce13bf1d984408f8f46a5d3a5b1);
            
        

        circle_marker_9465f2f9e255f95e50bbe893deae8103.bindPopup(popup_bf8452448b9eda97b1f8fa7f2e533d34)
        ;

        
    
    
            circle_marker_9465f2f9e255f95e50bbe893deae8103.bindTooltip(
                `<div>
                     OLINDA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5ad4af939deeafd23a926d7c06a0f516 = L.circleMarker(
                [-22.4331, -46.9581],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_4d684e7444ba94e6cc9cd87440bbc4ad = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1eedde39aa229c1c29ef760df13ebc7e = $(`<div id="html_1eedde39aa229c1c29ef760df13ebc7e" style="width: 100.0%; height: 100.0%;"><b>MOGI MIRIM (SP)</b><br>1 registros</div>`)[0];
                popup_4d684e7444ba94e6cc9cd87440bbc4ad.setContent(html_1eedde39aa229c1c29ef760df13ebc7e);
            
        

        circle_marker_5ad4af939deeafd23a926d7c06a0f516.bindPopup(popup_4d684e7444ba94e6cc9cd87440bbc4ad)
        ;

        
    
    
            circle_marker_5ad4af939deeafd23a926d7c06a0f516.bindTooltip(
                `<div>
                     MOGI MIRIM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_335aa41ad59b18bbfc62fd76b706fe75 = L.circleMarker(
                [-22.5931, -46.5281],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_aa4df1dfd345c193a7c2a92743e46fd3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6ef0783acbf051d33e7d78c283fdc131 = $(`<div id="html_6ef0783acbf051d33e7d78c283fdc131" style="width: 100.0%; height: 100.0%;"><b>SOCORRO (SP)</b><br>1 registros</div>`)[0];
                popup_aa4df1dfd345c193a7c2a92743e46fd3.setContent(html_6ef0783acbf051d33e7d78c283fdc131);
            
        

        circle_marker_335aa41ad59b18bbfc62fd76b706fe75.bindPopup(popup_aa4df1dfd345c193a7c2a92743e46fd3)
        ;

        
    
    
            circle_marker_335aa41ad59b18bbfc62fd76b706fe75.bindTooltip(
                `<div>
                     SOCORRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a049d35997fa8c3c12fb6e181be24f5b = L.circleMarker(
                [-19.8157, -43.9542],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_2cd874b539bb561bbc5ed5e3817f5d3b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1fcd638e206d9eea0548aed0ea2a4cc9 = $(`<div id="html_1fcd638e206d9eea0548aed0ea2a4cc9" style="width: 100.0%; height: 100.0%;"><b>BELO HORIZONTE (MG)</b><br>4 registros</div>`)[0];
                popup_2cd874b539bb561bbc5ed5e3817f5d3b.setContent(html_1fcd638e206d9eea0548aed0ea2a4cc9);
            
        

        circle_marker_a049d35997fa8c3c12fb6e181be24f5b.bindPopup(popup_2cd874b539bb561bbc5ed5e3817f5d3b)
        ;

        
    
    
            circle_marker_a049d35997fa8c3c12fb6e181be24f5b.bindTooltip(
                `<div>
                     BELO HORIZONTE: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b475ff637107547a38c611e8a3368112 = L.circleMarker(
                [-22.3831, -46.5664],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_b851b1880915cdb5d587694cebd5fe59 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_14d2b913fe050fb3a190b424a99cdadf = $(`<div id="html_14d2b913fe050fb3a190b424a99cdadf" style="width: 100.0%; height: 100.0%;"><b>MONTE SIAO (MG)</b><br>1 registros</div>`)[0];
                popup_b851b1880915cdb5d587694cebd5fe59.setContent(html_14d2b913fe050fb3a190b424a99cdadf);
            
        

        circle_marker_b475ff637107547a38c611e8a3368112.bindPopup(popup_b851b1880915cdb5d587694cebd5fe59)
        ;

        
    
    
            circle_marker_b475ff637107547a38c611e8a3368112.bindTooltip(
                `<div>
                     MONTE SIAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_bf32d7c821c39f84abef83eb34b2d64b = L.circleMarker(
                [-16.6481, -49.4881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_e8f61acc2981dd83b892eeef858f202f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_08a657f536e10d4a09bb3bc46215935e = $(`<div id="html_08a657f536e10d4a09bb3bc46215935e" style="width: 100.0%; height: 100.0%;"><b>TRINDADE (GO)</b><br>2 registros</div>`)[0];
                popup_e8f61acc2981dd83b892eeef858f202f.setContent(html_08a657f536e10d4a09bb3bc46215935e);
            
        

        circle_marker_bf32d7c821c39f84abef83eb34b2d64b.bindPopup(popup_e8f61acc2981dd83b892eeef858f202f)
        ;

        
    
    
            circle_marker_bf32d7c821c39f84abef83eb34b2d64b.bindTooltip(
                `<div>
                     TRINDADE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8a6a40100fad145fad0e8022d315a06b = L.circleMarker(
                [-15.7801, -47.9292],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkred", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_20372f54c0ba39e422946e9a1198e765 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5fbd735654f78e26066a22955736ad98 = $(`<div id="html_5fbd735654f78e26066a22955736ad98" style="width: 100.0%; height: 100.0%;"><b>BRASILIA (DF)</b><br>1 registros</div>`)[0];
                popup_20372f54c0ba39e422946e9a1198e765.setContent(html_5fbd735654f78e26066a22955736ad98);
            
        

        circle_marker_8a6a40100fad145fad0e8022d315a06b.bindPopup(popup_20372f54c0ba39e422946e9a1198e765)
        ;

        
    
    
            circle_marker_8a6a40100fad145fad0e8022d315a06b.bindTooltip(
                `<div>
                     BRASILIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_09775f2da870e0cc8842fe604463a6cb = L.circleMarker(
                [-23.6914, -46.5646],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_8eb2774bf4cd949271aefa8ecc0e1a7f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_815d05108d8a17dbbc88bbedd1e4d188 = $(`<div id="html_815d05108d8a17dbbc88bbedd1e4d188" style="width: 100.0%; height: 100.0%;"><b>SAO BERNARDO DO CAMPO (SP)</b><br>1 registros</div>`)[0];
                popup_8eb2774bf4cd949271aefa8ecc0e1a7f.setContent(html_815d05108d8a17dbbc88bbedd1e4d188);
            
        

        circle_marker_09775f2da870e0cc8842fe604463a6cb.bindPopup(popup_8eb2774bf4cd949271aefa8ecc0e1a7f)
        ;

        
    
    
            circle_marker_09775f2da870e0cc8842fe604463a6cb.bindTooltip(
                `<div>
                     SAO BERNARDO DO CAMPO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a4c66c214a5a82f1ad0166f31ef23347 = L.circleMarker(
                [-16.3281, -48.9531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_29d4cb8be75ff8736b1f1fc3ed30219d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_18784eddb1ead5cdb373b4f431ee03e3 = $(`<div id="html_18784eddb1ead5cdb373b4f431ee03e3" style="width: 100.0%; height: 100.0%;"><b>ANAPOLIS (GO)</b><br>1 registros</div>`)[0];
                popup_29d4cb8be75ff8736b1f1fc3ed30219d.setContent(html_18784eddb1ead5cdb373b4f431ee03e3);
            
        

        circle_marker_a4c66c214a5a82f1ad0166f31ef23347.bindPopup(popup_29d4cb8be75ff8736b1f1fc3ed30219d)
        ;

        
    
    
            circle_marker_a4c66c214a5a82f1ad0166f31ef23347.bindTooltip(
                `<div>
                     ANAPOLIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3b470448cbc0408d48e5330aa6942e96 = L.circleMarker(
                [-16.4706, -54.6364],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_7c1dfacd8320d0f7a459c2ae205a749c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_de58cbb2c108f3dd61b033070d73ee42 = $(`<div id="html_de58cbb2c108f3dd61b033070d73ee42" style="width: 100.0%; height: 100.0%;"><b>RONDONOPOLIS (MT)</b><br>2 registros</div>`)[0];
                popup_7c1dfacd8320d0f7a459c2ae205a749c.setContent(html_de58cbb2c108f3dd61b033070d73ee42);
            
        

        circle_marker_3b470448cbc0408d48e5330aa6942e96.bindPopup(popup_7c1dfacd8320d0f7a459c2ae205a749c)
        ;

        
    
    
            circle_marker_3b470448cbc0408d48e5330aa6942e96.bindTooltip(
                `<div>
                     RONDONOPOLIS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c079f3e8c7d6e1ca513129975ef81c20 = L.circleMarker(
                [-23.6236, -46.5547],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c89af396b11756a7d336a119a73e5ced = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3e5c6a1ab256a65e465e01e36729d136 = $(`<div id="html_3e5c6a1ab256a65e465e01e36729d136" style="width: 100.0%; height: 100.0%;"><b>SAO CAETANO DO SUL (SP)</b><br>1 registros</div>`)[0];
                popup_c89af396b11756a7d336a119a73e5ced.setContent(html_3e5c6a1ab256a65e465e01e36729d136);
            
        

        circle_marker_c079f3e8c7d6e1ca513129975ef81c20.bindPopup(popup_c89af396b11756a7d336a119a73e5ced)
        ;

        
    
    
            circle_marker_c079f3e8c7d6e1ca513129975ef81c20.bindTooltip(
                `<div>
                     SAO CAETANO DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b7e13e75f4d013d2de22b9d4f8d9f1ef = L.circleMarker(
                [-23.8131, -45.4031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_97dfb8a8410bce13dc991666d7cc1139 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1924b0dc8cbfa298d3d86076796f6789 = $(`<div id="html_1924b0dc8cbfa298d3d86076796f6789" style="width: 100.0%; height: 100.0%;"><b>SAO SEBASTIAO (SP)</b><br>1 registros</div>`)[0];
                popup_97dfb8a8410bce13dc991666d7cc1139.setContent(html_1924b0dc8cbfa298d3d86076796f6789);
            
        

        circle_marker_b7e13e75f4d013d2de22b9d4f8d9f1ef.bindPopup(popup_97dfb8a8410bce13dc991666d7cc1139)
        ;

        
    
    
            circle_marker_b7e13e75f4d013d2de22b9d4f8d9f1ef.bindTooltip(
                `<div>
                     SAO SEBASTIAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5fcff3a9bfab8b2782a88315cce64279 = L.circleMarker(
                [-22.5981, -48.8031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_184ebfa79f84cff03466a2c1aef32ecf = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c22f4c23fa9e19b61b04e6b9eef98f4b = $(`<div id="html_c22f4c23fa9e19b61b04e6b9eef98f4b" style="width: 100.0%; height: 100.0%;"><b>LENCOIS PAULISTA (SP)</b><br>1 registros</div>`)[0];
                popup_184ebfa79f84cff03466a2c1aef32ecf.setContent(html_c22f4c23fa9e19b61b04e6b9eef98f4b);
            
        

        circle_marker_5fcff3a9bfab8b2782a88315cce64279.bindPopup(popup_184ebfa79f84cff03466a2c1aef32ecf)
        ;

        
    
    
            circle_marker_5fcff3a9bfab8b2782a88315cce64279.bindTooltip(
                `<div>
                     LENCOIS PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_97d161eb2f2e3edf918b83e9d5a698b0 = L.circleMarker(
                [-22.2831, -46.3664],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_2e04eda19fa7664e06b7c876ec299678 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8baaa48abe5330edca391195eac12009 = $(`<div id="html_8baaa48abe5330edca391195eac12009" style="width: 100.0%; height: 100.0%;"><b>OURO FINO (MG)</b><br>1 registros</div>`)[0];
                popup_2e04eda19fa7664e06b7c876ec299678.setContent(html_8baaa48abe5330edca391195eac12009);
            
        

        circle_marker_97d161eb2f2e3edf918b83e9d5a698b0.bindPopup(popup_2e04eda19fa7664e06b7c876ec299678)
        ;

        
    
    
            circle_marker_97d161eb2f2e3edf918b83e9d5a698b0.bindTooltip(
                `<div>
                     OURO FINO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8fdd44a2ae4b4e48670d931fc87ee80f = L.circleMarker(
                [-13.8331, -56.0831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_926d945e8f821b5845a93c837a7af8c3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_71b74db4145b29df0dbcc2a1cbef8311 = $(`<div id="html_71b74db4145b29df0dbcc2a1cbef8311" style="width: 100.0%; height: 100.0%;"><b>NOVA MUTUM (MT)</b><br>1 registros</div>`)[0];
                popup_926d945e8f821b5845a93c837a7af8c3.setContent(html_71b74db4145b29df0dbcc2a1cbef8311);
            
        

        circle_marker_8fdd44a2ae4b4e48670d931fc87ee80f.bindPopup(popup_926d945e8f821b5845a93c837a7af8c3)
        ;

        
    
    
            circle_marker_8fdd44a2ae4b4e48670d931fc87ee80f.bindTooltip(
                `<div>
                     NOVA MUTUM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4dad047d8910e3573909bb2d8f293873 = L.circleMarker(
                [-23.0131, -48.0131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_852596ba50fdae4dede5e756a32eb65d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e2e4d2cb75591baaf7e038e482a11c83 = $(`<div id="html_e2e4d2cb75591baaf7e038e482a11c83" style="width: 100.0%; height: 100.0%;"><b>CONCHAS (SP)</b><br>1 registros</div>`)[0];
                popup_852596ba50fdae4dede5e756a32eb65d.setContent(html_e2e4d2cb75591baaf7e038e482a11c83);
            
        

        circle_marker_4dad047d8910e3573909bb2d8f293873.bindPopup(popup_852596ba50fdae4dede5e756a32eb65d)
        ;

        
    
    
            circle_marker_4dad047d8910e3573909bb2d8f293873.bindTooltip(
                `<div>
                     CONCHAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_dd5a56be868348a568275455268736c0 = L.circleMarker(
                [-3.7319, -38.5267],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkgreen", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_fefd22d7ce6763de28a196d0d049a45f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_43d1a670caaedcaa73709a7978ab0a27 = $(`<div id="html_43d1a670caaedcaa73709a7978ab0a27" style="width: 100.0%; height: 100.0%;"><b>FORTALEZA (CE)</b><br>1 registros</div>`)[0];
                popup_fefd22d7ce6763de28a196d0d049a45f.setContent(html_43d1a670caaedcaa73709a7978ab0a27);
            
        

        circle_marker_dd5a56be868348a568275455268736c0.bindPopup(popup_fefd22d7ce6763de28a196d0d049a45f)
        ;

        
    
    
            circle_marker_dd5a56be868348a568275455268736c0.bindTooltip(
                `<div>
                     FORTALEZA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4c91dde6f413d83f994ffe8daf8c46d5 = L.circleMarker(
                [-16.7031, -49.0931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_ebbee48b5848dea78c62279cf3d774dd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_bbcfe37387218592cfef8dc2066f430e = $(`<div id="html_bbcfe37387218592cfef8dc2066f430e" style="width: 100.0%; height: 100.0%;"><b>SENADOR CANEDO (GO)</b><br>1 registros</div>`)[0];
                popup_ebbee48b5848dea78c62279cf3d774dd.setContent(html_bbcfe37387218592cfef8dc2066f430e);
            
        

        circle_marker_4c91dde6f413d83f994ffe8daf8c46d5.bindPopup(popup_ebbee48b5848dea78c62279cf3d774dd)
        ;

        
    
    
            circle_marker_4c91dde6f413d83f994ffe8daf8c46d5.bindTooltip(
                `<div>
                     SENADOR CANEDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ef905e141d12936d43163444a3195ddb = L.circleMarker(
                [-22.4381, -46.8231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a75763106d2ec1d0a6f3e5d4dc21e88a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5ab3b734e4c7c529f22968a407edfa26 = $(`<div id="html_5ab3b734e4c7c529f22968a407edfa26" style="width: 100.0%; height: 100.0%;"><b>ITAPIRA (SP)</b><br>1 registros</div>`)[0];
                popup_a75763106d2ec1d0a6f3e5d4dc21e88a.setContent(html_5ab3b734e4c7c529f22968a407edfa26);
            
        

        circle_marker_ef905e141d12936d43163444a3195ddb.bindPopup(popup_a75763106d2ec1d0a6f3e5d4dc21e88a)
        ;

        
    
    
            circle_marker_ef905e141d12936d43163444a3195ddb.bindTooltip(
                `<div>
                     ITAPIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d8c9bc90daabece58138d0d00938c575 = L.circleMarker(
                [-20.4644, -45.4264],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_ecef51d018f270f3ca1fd3866c630c99 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cfcfbbf20f0ca7e8cb955346d163d9f0 = $(`<div id="html_cfcfbbf20f0ca7e8cb955346d163d9f0" style="width: 100.0%; height: 100.0%;"><b>FORMIGA (MG)</b><br>1 registros</div>`)[0];
                popup_ecef51d018f270f3ca1fd3866c630c99.setContent(html_cfcfbbf20f0ca7e8cb955346d163d9f0);
            
        

        circle_marker_d8c9bc90daabece58138d0d00938c575.bindPopup(popup_ecef51d018f270f3ca1fd3866c630c99)
        ;

        
    
    
            circle_marker_d8c9bc90daabece58138d0d00938c575.bindTooltip(
                `<div>
                     FORMIGA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2d1c9751d742b0bc1d26b6d12a67196e = L.circleMarker(
                [-20.2331, -46.3731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_35177e8615656ea5709d2980a880b731 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_620a8c0cf1e8188c8feac64bc5acd2c0 = $(`<div id="html_620a8c0cf1e8188c8feac64bc5acd2c0" style="width: 100.0%; height: 100.0%;"><b>SAO ROQUE DE MINAS (MG)</b><br>1 registros</div>`)[0];
                popup_35177e8615656ea5709d2980a880b731.setContent(html_620a8c0cf1e8188c8feac64bc5acd2c0);
            
        

        circle_marker_2d1c9751d742b0bc1d26b6d12a67196e.bindPopup(popup_35177e8615656ea5709d2980a880b731)
        ;

        
    
    
            circle_marker_2d1c9751d742b0bc1d26b6d12a67196e.bindTooltip(
                `<div>
                     SAO ROQUE DE MINAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0b67ae8bf7bd24890d8fa1ec09a16485 = L.circleMarker(
                [-23.0281, -46.9731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_482be6bf7906a15bcc9b4db79aa40279 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_28362cd602700ca0f170e8d810c91985 = $(`<div id="html_28362cd602700ca0f170e8d810c91985" style="width: 100.0%; height: 100.0%;"><b>VINHEDO (SP)</b><br>1 registros</div>`)[0];
                popup_482be6bf7906a15bcc9b4db79aa40279.setContent(html_28362cd602700ca0f170e8d810c91985);
            
        

        circle_marker_0b67ae8bf7bd24890d8fa1ec09a16485.bindPopup(popup_482be6bf7906a15bcc9b4db79aa40279)
        ;

        
    
    
            circle_marker_0b67ae8bf7bd24890d8fa1ec09a16485.bindTooltip(
                `<div>
                     VINHEDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_91255195d2f6ccf4dd0d21f6a9418248 = L.circleMarker(
                [-22.9068, -43.1729],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_cfa52c3ae37e796376076216270431f5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c2268a8bc97a265df3136cd8fdb9274b = $(`<div id="html_c2268a8bc97a265df3136cd8fdb9274b" style="width: 100.0%; height: 100.0%;"><b>RIO DE JANEIRO (RJ)</b><br>1 registros</div>`)[0];
                popup_cfa52c3ae37e796376076216270431f5.setContent(html_c2268a8bc97a265df3136cd8fdb9274b);
            
        

        circle_marker_91255195d2f6ccf4dd0d21f6a9418248.bindPopup(popup_cfa52c3ae37e796376076216270431f5)
        ;

        
    
    
            circle_marker_91255195d2f6ccf4dd0d21f6a9418248.bindTooltip(
                `<div>
                     RIO DE JANEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f3903b06c6d23c2ceebcacbafa4a0b81 = L.circleMarker(
                [-23.2031, -47.2831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_2dce71c61daed9c53d4bf4a145be33d7 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_70597f0be6a61161c064c331dde323c3 = $(`<div id="html_70597f0be6a61161c064c331dde323c3" style="width: 100.0%; height: 100.0%;"><b>SALTO (SP)</b><br>1 registros</div>`)[0];
                popup_2dce71c61daed9c53d4bf4a145be33d7.setContent(html_70597f0be6a61161c064c331dde323c3);
            
        

        circle_marker_f3903b06c6d23c2ceebcacbafa4a0b81.bindPopup(popup_2dce71c61daed9c53d4bf4a145be33d7)
        ;

        
    
    
            circle_marker_f3903b06c6d23c2ceebcacbafa4a0b81.bindTooltip(
                `<div>
                     SALTO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_bcd86bcfc467056cda60eeded132ef0a = L.circleMarker(
                [-11.5031, -54.8831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_698f140f7d35804419ef61acd986725d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d2054846f951637c5f39fd7913cb89ef = $(`<div id="html_d2054846f951637c5f39fd7913cb89ef" style="width: 100.0%; height: 100.0%;"><b>CLAUDIA (MT)</b><br>1 registros</div>`)[0];
                popup_698f140f7d35804419ef61acd986725d.setContent(html_d2054846f951637c5f39fd7913cb89ef);
            
        

        circle_marker_bcd86bcfc467056cda60eeded132ef0a.bindPopup(popup_698f140f7d35804419ef61acd986725d)
        ;

        
    
    
            circle_marker_bcd86bcfc467056cda60eeded132ef0a.bindTooltip(
                `<div>
                     CLAUDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b85e7603895bf1493a2ca9a93fa84ff4 = L.circleMarker(
                [-16.6869, -49.2648],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_47d0bc64c6aac5c61ed9e29e177b0fed = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_23e6f3cc75a0b3e81576ba2ec60c76f1 = $(`<div id="html_23e6f3cc75a0b3e81576ba2ec60c76f1" style="width: 100.0%; height: 100.0%;"><b>GOIANIA (GO)</b><br>2 registros</div>`)[0];
                popup_47d0bc64c6aac5c61ed9e29e177b0fed.setContent(html_23e6f3cc75a0b3e81576ba2ec60c76f1);
            
        

        circle_marker_b85e7603895bf1493a2ca9a93fa84ff4.bindPopup(popup_47d0bc64c6aac5c61ed9e29e177b0fed)
        ;

        
    
    
            circle_marker_b85e7603895bf1493a2ca9a93fa84ff4.bindTooltip(
                `<div>
                     GOIANIA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3206734b45768040c099a7e725ec048b = L.circleMarker(
                [-16.7353, -43.8619],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_fb8339ef9a5af7e1f00dd829cc4f62d8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0690518605a60023be594c1f5e2bf7a4 = $(`<div id="html_0690518605a60023be594c1f5e2bf7a4" style="width: 100.0%; height: 100.0%;"><b>MONTES CLAROS (MG)</b><br>1 registros</div>`)[0];
                popup_fb8339ef9a5af7e1f00dd829cc4f62d8.setContent(html_0690518605a60023be594c1f5e2bf7a4);
            
        

        circle_marker_3206734b45768040c099a7e725ec048b.bindPopup(popup_fb8339ef9a5af7e1f00dd829cc4f62d8)
        ;

        
    
    
            circle_marker_3206734b45768040c099a7e725ec048b.bindTooltip(
                `<div>
                     MONTES CLAROS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2a083d064c75b77a7653a8907a94f1af = L.circleMarker(
                [-10.9472, -37.0731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "lightgray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_995729506b03b291d6b065505736dd9d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_82f5acb04db3bc2f885dba5b5455f969 = $(`<div id="html_82f5acb04db3bc2f885dba5b5455f969" style="width: 100.0%; height: 100.0%;"><b>ARACAJU (SE)</b><br>1 registros</div>`)[0];
                popup_995729506b03b291d6b065505736dd9d.setContent(html_82f5acb04db3bc2f885dba5b5455f969);
            
        

        circle_marker_2a083d064c75b77a7653a8907a94f1af.bindPopup(popup_995729506b03b291d6b065505736dd9d)
        ;

        
    
    
            circle_marker_2a083d064c75b77a7653a8907a94f1af.bindTooltip(
                `<div>
                     ARACAJU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_36dbc552e656f9a735df29bcaa31786e = L.circleMarker(
                [-18.1681, -47.9481],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_c1bf5339630a5ca1e8631b5a23e53d15 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6e87687b99de96ecfde9f93ff4853a7d = $(`<div id="html_6e87687b99de96ecfde9f93ff4853a7d" style="width: 100.0%; height: 100.0%;"><b>CATALAO (GO)</b><br>1 registros</div>`)[0];
                popup_c1bf5339630a5ca1e8631b5a23e53d15.setContent(html_6e87687b99de96ecfde9f93ff4853a7d);
            
        

        circle_marker_36dbc552e656f9a735df29bcaa31786e.bindPopup(popup_c1bf5339630a5ca1e8631b5a23e53d15)
        ;

        
    
    
            circle_marker_36dbc552e656f9a735df29bcaa31786e.bindTooltip(
                `<div>
                     CATALAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b0e22e553528190ac45df6c7026c2ecf = L.circleMarker(
                [-14.6231, -57.5031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_9303959d4a75243846e1860bbbf05b81 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8f84941ccbf67a7551f3c274b70622b5 = $(`<div id="html_8f84941ccbf67a7551f3c274b70622b5" style="width: 100.0%; height: 100.0%;"><b>TANGARA DA SERRA (MT)</b><br>1 registros</div>`)[0];
                popup_9303959d4a75243846e1860bbbf05b81.setContent(html_8f84941ccbf67a7551f3c274b70622b5);
            
        

        circle_marker_b0e22e553528190ac45df6c7026c2ecf.bindPopup(popup_9303959d4a75243846e1860bbbf05b81)
        ;

        
    
    
            circle_marker_b0e22e553528190ac45df6c7026c2ecf.bindTooltip(
                `<div>
                     TANGARA DA SERRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7263a9b6d7c0475d81ff363409b4566f = L.circleMarker(
                [-7.5764, -40.4975],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_278bc7b2c69602b3481900749f485c6c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6bfb062c42e52ecc0ea2cd2170c5a0fc = $(`<div id="html_6bfb062c42e52ecc0ea2cd2170c5a0fc" style="width: 100.0%; height: 100.0%;"><b>ARARIPINA (PE)</b><br>1 registros</div>`)[0];
                popup_278bc7b2c69602b3481900749f485c6c.setContent(html_6bfb062c42e52ecc0ea2cd2170c5a0fc);
            
        

        circle_marker_7263a9b6d7c0475d81ff363409b4566f.bindPopup(popup_278bc7b2c69602b3481900749f485c6c)
        ;

        
    
    
            circle_marker_7263a9b6d7c0475d81ff363409b4566f.bindTooltip(
                `<div>
                     ARARIPINA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_71848f5338330661a7306883280012fd = L.circleMarker(
                [-9.4111, -40.4986],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "lightred", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_5e7a52e1d5530cdc3e9a0aa1c92efd57 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_36ed6dd1396bf2c10fb0656c22b32482 = $(`<div id="html_36ed6dd1396bf2c10fb0656c22b32482" style="width: 100.0%; height: 100.0%;"><b>JUAZEIRO (BA)</b><br>1 registros</div>`)[0];
                popup_5e7a52e1d5530cdc3e9a0aa1c92efd57.setContent(html_36ed6dd1396bf2c10fb0656c22b32482);
            
        

        circle_marker_71848f5338330661a7306883280012fd.bindPopup(popup_5e7a52e1d5530cdc3e9a0aa1c92efd57)
        ;

        
    
    
            circle_marker_71848f5338330661a7306883280012fd.bindTooltip(
                `<div>
                     JUAZEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5a5380ee6d68094a31c0d82663b242bc = L.circleMarker(
                [-19.9317, -44.0536],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_260002549824d9349b333dce97ead5fc = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_87fce6c51d3342c0e0c8c96b1742c1bf = $(`<div id="html_87fce6c51d3342c0e0c8c96b1742c1bf" style="width: 100.0%; height: 100.0%;"><b>CONTAGEM (MG)</b><br>1 registros</div>`)[0];
                popup_260002549824d9349b333dce97ead5fc.setContent(html_87fce6c51d3342c0e0c8c96b1742c1bf);
            
        

        circle_marker_5a5380ee6d68094a31c0d82663b242bc.bindPopup(popup_260002549824d9349b333dce97ead5fc)
        ;

        
    
    
            circle_marker_5a5380ee6d68094a31c0d82663b242bc.bindTooltip(
                `<div>
                     CONTAGEM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2f4a9b9b19057836179c2375ba6fd430 = L.circleMarker(
                [-3.5431, -40.6431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkgreen", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_7bc33ab299a681e5d976f13f7bdd9798 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5e9f18537aafd659e279d9bb5f67aeac = $(`<div id="html_5e9f18537aafd659e279d9bb5f67aeac" style="width: 100.0%; height: 100.0%;"><b>COREAU (CE)</b><br>1 registros</div>`)[0];
                popup_7bc33ab299a681e5d976f13f7bdd9798.setContent(html_5e9f18537aafd659e279d9bb5f67aeac);
            
        

        circle_marker_2f4a9b9b19057836179c2375ba6fd430.bindPopup(popup_7bc33ab299a681e5d976f13f7bdd9798)
        ;

        
    
    
            circle_marker_2f4a9b9b19057836179c2375ba6fd430.bindTooltip(
                `<div>
                     COREAU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a85aa51288e3f7e0faa4a18a0c36b5d1 = L.circleMarker(
                [-22.6631, -50.4131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_718dcd73c5886a5ad36d0fba0c249315 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fec645944c86923b9c1f64099c3e9f6a = $(`<div id="html_fec645944c86923b9c1f64099c3e9f6a" style="width: 100.0%; height: 100.0%;"><b>ASSIS (SP)</b><br>1 registros</div>`)[0];
                popup_718dcd73c5886a5ad36d0fba0c249315.setContent(html_fec645944c86923b9c1f64099c3e9f6a);
            
        

        circle_marker_a85aa51288e3f7e0faa4a18a0c36b5d1.bindPopup(popup_718dcd73c5886a5ad36d0fba0c249315)
        ;

        
    
    
            circle_marker_a85aa51288e3f7e0faa4a18a0c36b5d1.bindTooltip(
                `<div>
                     ASSIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2c6a6455d8ada4dac93eab6df1452e37 = L.circleMarker(
                [-26.8231, -49.2731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_b4eff4e966d4938d2ec228eb786b5b6d);
        
    
        var popup_a6daccc05c84780481a4b54b1cd4c7e3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_032fca425c8161ba7b845938c249de80 = $(`<div id="html_032fca425c8161ba7b845938c249de80" style="width: 100.0%; height: 100.0%;"><b>TIMBO (SC)</b><br>1 registros</div>`)[0];
                popup_a6daccc05c84780481a4b54b1cd4c7e3.setContent(html_032fca425c8161ba7b845938c249de80);
            
        

        circle_marker_2c6a6455d8ada4dac93eab6df1452e37.bindPopup(popup_a6daccc05c84780481a4b54b1cd4c7e3)
        ;

        
    
    
            circle_marker_2c6a6455d8ada4dac93eab6df1452e37.bindTooltip(
                `<div>
                     TIMBO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
</script>
</html>