<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_edd58de8f0bba98333e51ce17b247028 {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
    <div style="position: fixed;
                bottom: 50px; left: 50px; width: 280px; height: 160px;
                background-color: white; border:2px solid grey; z-index:9999;
                font-size:12px; padding: 10px; border-radius: 5px;">
    <p><b>🗺️ Mapa do Brasil - Municípios</b></p>
    <p><b>Estatísticas:</b></p>
    <p>• Total de municípios: 128</p>
    <p>• Total de registros: 235</p>
    <p>• Estados representados: 14</p>
    <p><b>Legenda:</b></p>
    <p>• Tamanho do círculo = Número de registros</p>
    <p>• Cores diferentes = Estados</p>
    <p>• Clique nos pontos para mais detalhes</p>
    </div>
    
    
            <div class="folium-map" id="map_edd58de8f0bba98333e51ce17b247028" ></div>
        
</body>
<script>
    
    
            var map_edd58de8f0bba98333e51ce17b247028 = L.map(
                "map_edd58de8f0bba98333e51ce17b247028",
                {
                    center: [-14.235, -51.9253],
                    crs: L.CRS.EPSG3857,
                    ...{
  "maxBounds": [
[
-90,
-180,
],
[
90,
180,
],
],
  "zoom": 5,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_656ddd64fc5ec047d9d4f59b3af96f87 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 4,
  "maxZoom": 12,
  "maxNativeZoom": 12,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_656ddd64fc5ec047d9d4f59b3af96f87.addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
            map_edd58de8f0bba98333e51ce17b247028.fitBounds(
                [[-33.75, -73.98], [5.27, -34.79]],
                {}
            );
        
    
            var circle_marker_e4c822315c7dc4ca74fcce1b8bb42d7f = L.circleMarker(
                [-23.4538, -46.5333],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 24, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a3a05bcc2793704fdf4ee6553c3c5c43 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_585be556ba3e298613fede0e3f542b96 = $(`<div id="html_585be556ba3e298613fede0e3f542b96" style="width: 100.0%; height: 100.0%;"><b>GUARULHOS (SP)</b><br>8 registros</div>`)[0];
                popup_a3a05bcc2793704fdf4ee6553c3c5c43.setContent(html_585be556ba3e298613fede0e3f542b96);
            
        

        circle_marker_e4c822315c7dc4ca74fcce1b8bb42d7f.bindPopup(popup_a3a05bcc2793704fdf4ee6553c3c5c43)
        ;

        
    
    
            circle_marker_e4c822315c7dc4ca74fcce1b8bb42d7f.bindTooltip(
                `<div>
                     GUARULHOS: 8
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f97bb8a2707c7862176508054eefff34 = L.circleMarker(
                [-22.9456, -47.3156],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a4a42ed6713df52cfbe2a54636dc64c7 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d6be1fa8e7520e3a4b7d47bb34ab3102 = $(`<div id="html_d6be1fa8e7520e3a4b7d47bb34ab3102" style="width: 100.0%; height: 100.0%;"><b>MONTE MOR (SP)</b><br>2 registros</div>`)[0];
                popup_a4a42ed6713df52cfbe2a54636dc64c7.setContent(html_d6be1fa8e7520e3a4b7d47bb34ab3102);
            
        

        circle_marker_f97bb8a2707c7862176508054eefff34.bindPopup(popup_a4a42ed6713df52cfbe2a54636dc64c7)
        ;

        
    
    
            circle_marker_f97bb8a2707c7862176508054eefff34.bindTooltip(
                `<div>
                     MONTE MOR: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9e180e1f263b6a1ab5af42df78da3a2b = L.circleMarker(
                [-23.0922, -47.2181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_6070c8e63063be020212ec3816f921f8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_80776a50d64b0ad9ee6c6d21257b5113 = $(`<div id="html_80776a50d64b0ad9ee6c6d21257b5113" style="width: 100.0%; height: 100.0%;"><b>INDAIATUBA (SP)</b><br>5 registros</div>`)[0];
                popup_6070c8e63063be020212ec3816f921f8.setContent(html_80776a50d64b0ad9ee6c6d21257b5113);
            
        

        circle_marker_9e180e1f263b6a1ab5af42df78da3a2b.bindPopup(popup_6070c8e63063be020212ec3816f921f8)
        ;

        
    
    
            circle_marker_9e180e1f263b6a1ab5af42df78da3a2b.bindTooltip(
                `<div>
                     INDAIATUBA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b1b390545d73ca05715ed51df8d08087 = L.circleMarker(
                [-23.5015, -47.4526],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 30, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_82bf656edfac642a065731dde9fa59d6 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_97b3f10050fd87796914d6b37894c3e3 = $(`<div id="html_97b3f10050fd87796914d6b37894c3e3" style="width: 100.0%; height: 100.0%;"><b>SOROCABA (SP)</b><br>10 registros</div>`)[0];
                popup_82bf656edfac642a065731dde9fa59d6.setContent(html_97b3f10050fd87796914d6b37894c3e3);
            
        

        circle_marker_b1b390545d73ca05715ed51df8d08087.bindPopup(popup_82bf656edfac642a065731dde9fa59d6)
        ;

        
    
    
            circle_marker_b1b390545d73ca05715ed51df8d08087.bindTooltip(
                `<div>
                     SOROCABA: 10
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_03d8440cb9c334504c4d32041c48c74e = L.circleMarker(
                [-23.2156, -47.5281],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_438c1bc80ac5560ea249b8421ecc6067 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e094f8e618ab8285b78d7cb5e9a9b45a = $(`<div id="html_e094f8e618ab8285b78d7cb5e9a9b45a" style="width: 100.0%; height: 100.0%;"><b>PORTO FELIZ (SP)</b><br>1 registros</div>`)[0];
                popup_438c1bc80ac5560ea249b8421ecc6067.setContent(html_e094f8e618ab8285b78d7cb5e9a9b45a);
            
        

        circle_marker_03d8440cb9c334504c4d32041c48c74e.bindPopup(popup_438c1bc80ac5560ea249b8421ecc6067)
        ;

        
    
    
            circle_marker_03d8440cb9c334504c4d32041c48c74e.bindTooltip(
                `<div>
                     PORTO FELIZ: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b81fa5da10399f01a4488aa01f5e72f6 = L.circleMarker(
                [-23.3531, -47.8656],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_9b6f139e6a262524b2ba3d0271a7335e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c750781616799ea5702a0f75cd59a367 = $(`<div id="html_c750781616799ea5702a0f75cd59a367" style="width: 100.0%; height: 100.0%;"><b>TATUI (SP)</b><br>2 registros</div>`)[0];
                popup_9b6f139e6a262524b2ba3d0271a7335e.setContent(html_c750781616799ea5702a0f75cd59a367);
            
        

        circle_marker_b81fa5da10399f01a4488aa01f5e72f6.bindPopup(popup_9b6f139e6a262524b2ba3d0271a7335e)
        ;

        
    
    
            circle_marker_b81fa5da10399f01a4488aa01f5e72f6.bindTooltip(
                `<div>
                     TATUI: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d576ed4abc515c44a1e4ba6d9109876b = L.circleMarker(
                [-23.2831, -47.6731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_aabd8f77d088e5d65afd5d04524ddbaa = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0a420a63da56124b4c93802a59c952f5 = $(`<div id="html_0a420a63da56124b4c93802a59c952f5" style="width: 100.0%; height: 100.0%;"><b>BOITUVA (SP)</b><br>4 registros</div>`)[0];
                popup_aabd8f77d088e5d65afd5d04524ddbaa.setContent(html_0a420a63da56124b4c93802a59c952f5);
            
        

        circle_marker_d576ed4abc515c44a1e4ba6d9109876b.bindPopup(popup_aabd8f77d088e5d65afd5d04524ddbaa)
        ;

        
    
    
            circle_marker_d576ed4abc515c44a1e4ba6d9109876b.bindTooltip(
                `<div>
                     BOITUVA: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6b2cf956e99c142260487140bd57c072 = L.circleMarker(
                [-22.9056, -47.0608],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 27, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_861a267a1d3b61adfeea5f70f10e3d07 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b062a49e4b6f2247db072aa384ae62ce = $(`<div id="html_b062a49e4b6f2247db072aa384ae62ce" style="width: 100.0%; height: 100.0%;"><b>CAMPINAS (SP)</b><br>9 registros</div>`)[0];
                popup_861a267a1d3b61adfeea5f70f10e3d07.setContent(html_b062a49e4b6f2247db072aa384ae62ce);
            
        

        circle_marker_6b2cf956e99c142260487140bd57c072.bindPopup(popup_861a267a1d3b61adfeea5f70f10e3d07)
        ;

        
    
    
            circle_marker_6b2cf956e99c142260487140bd57c072.bindTooltip(
                `<div>
                     CAMPINAS: 9
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c8f03cf4938d38e8c629df0d404b26b1 = L.circleMarker(
                [-21.5931, -48.8156],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_ce486197a9cbe205bfc581f64811a2de = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8ee3df1ee43c5b6e0b4a3667660c60d2 = $(`<div id="html_8ee3df1ee43c5b6e0b4a3667660c60d2" style="width: 100.0%; height: 100.0%;"><b>ITAPOLIS (SP)</b><br>1 registros</div>`)[0];
                popup_ce486197a9cbe205bfc581f64811a2de.setContent(html_8ee3df1ee43c5b6e0b4a3667660c60d2);
            
        

        circle_marker_c8f03cf4938d38e8c629df0d404b26b1.bindPopup(popup_ce486197a9cbe205bfc581f64811a2de)
        ;

        
    
    
            circle_marker_c8f03cf4938d38e8c629df0d404b26b1.bindTooltip(
                `<div>
                     ITAPOLIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3d5302a9a50ad66be0cb23c9a2c8a6a6 = L.circleMarker(
                [-21.7947, -48.1756],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_47554476ce823eb7ca9922bcf7f36a88 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_30b55fb1507313d31ed775eac081d04e = $(`<div id="html_30b55fb1507313d31ed775eac081d04e" style="width: 100.0%; height: 100.0%;"><b>ARARAQUARA (SP)</b><br>5 registros</div>`)[0];
                popup_47554476ce823eb7ca9922bcf7f36a88.setContent(html_30b55fb1507313d31ed775eac081d04e);
            
        

        circle_marker_3d5302a9a50ad66be0cb23c9a2c8a6a6.bindPopup(popup_47554476ce823eb7ca9922bcf7f36a88)
        ;

        
    
    
            circle_marker_3d5302a9a50ad66be0cb23c9a2c8a6a6.bindTooltip(
                `<div>
                     ARARAQUARA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_e62d6604350a11b361ec3f3e0a531854 = L.circleMarker(
                [-23.5505, -46.6333],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 45, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_ca75d03a794e0650d48ca9331fcbe7c9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d4b0d292889b625ac22cb3abcf90f658 = $(`<div id="html_d4b0d292889b625ac22cb3abcf90f658" style="width: 100.0%; height: 100.0%;"><b>SAO PAULO (SP)</b><br>15 registros</div>`)[0];
                popup_ca75d03a794e0650d48ca9331fcbe7c9.setContent(html_d4b0d292889b625ac22cb3abcf90f658);
            
        

        circle_marker_e62d6604350a11b361ec3f3e0a531854.bindPopup(popup_ca75d03a794e0650d48ca9331fcbe7c9)
        ;

        
    
    
            circle_marker_e62d6604350a11b361ec3f3e0a531854.bindTooltip(
                `<div>
                     SAO PAULO: 15
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9a8c7c2f5eb131476d1c794d5757c41c = L.circleMarker(
                [-22.7394, -47.3314],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_f129ec92de2a6ffa992539588cbf6411 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_619a6f960093c229fe360aead6b51815 = $(`<div id="html_619a6f960093c229fe360aead6b51815" style="width: 100.0%; height: 100.0%;"><b>AMERICANA (SP)</b><br>1 registros</div>`)[0];
                popup_f129ec92de2a6ffa992539588cbf6411.setContent(html_619a6f960093c229fe360aead6b51815);
            
        

        circle_marker_9a8c7c2f5eb131476d1c794d5757c41c.bindPopup(popup_f129ec92de2a6ffa992539588cbf6411)
        ;

        
    
    
            circle_marker_9a8c7c2f5eb131476d1c794d5757c41c.bindTooltip(
                `<div>
                     AMERICANA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_482e38c6f9e09ae3ecca680976bdbb3f = L.circleMarker(
                [-22.5731, -47.1731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_3bfacefb2f91175993733d411827dbc2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ae0424b43987726bb763c4a672c64e85 = $(`<div id="html_ae0424b43987726bb763c4a672c64e85" style="width: 100.0%; height: 100.0%;"><b>ARTUR NOGUEIRA (SP)</b><br>2 registros</div>`)[0];
                popup_3bfacefb2f91175993733d411827dbc2.setContent(html_ae0424b43987726bb763c4a672c64e85);
            
        

        circle_marker_482e38c6f9e09ae3ecca680976bdbb3f.bindPopup(popup_3bfacefb2f91175993733d411827dbc2)
        ;

        
    
    
            circle_marker_482e38c6f9e09ae3ecca680976bdbb3f.bindTooltip(
                `<div>
                     ARTUR NOGUEIRA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8d89e42eb8074b6c11081323587bf58d = L.circleMarker(
                [-22.6381, -47.0531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_edf5ff63295121a6119776bd3e32acbf = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_705985e2b83ac74903c9175457f0bbd8 = $(`<div id="html_705985e2b83ac74903c9175457f0bbd8" style="width: 100.0%; height: 100.0%;"><b>HOLAMBRA (SP)</b><br>1 registros</div>`)[0];
                popup_edf5ff63295121a6119776bd3e32acbf.setContent(html_705985e2b83ac74903c9175457f0bbd8);
            
        

        circle_marker_8d89e42eb8074b6c11081323587bf58d.bindPopup(popup_edf5ff63295121a6119776bd3e32acbf)
        ;

        
    
    
            circle_marker_8d89e42eb8074b6c11081323587bf58d.bindTooltip(
                `<div>
                     HOLAMBRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7cba01daa84efdf059655b5b0809d63c = L.circleMarker(
                [-20.2831, -50.2431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_6f9af82b5772e932918a87ea7f6ac351 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a724f0f50c5ff90bc072d13dccf9cd64 = $(`<div id="html_a724f0f50c5ff90bc072d13dccf9cd64" style="width: 100.0%; height: 100.0%;"><b>FERNANDOPOLIS (SP)</b><br>2 registros</div>`)[0];
                popup_6f9af82b5772e932918a87ea7f6ac351.setContent(html_a724f0f50c5ff90bc072d13dccf9cd64);
            
        

        circle_marker_7cba01daa84efdf059655b5b0809d63c.bindPopup(popup_6f9af82b5772e932918a87ea7f6ac351)
        ;

        
    
    
            circle_marker_7cba01daa84efdf059655b5b0809d63c.bindTooltip(
                `<div>
                     FERNANDOPOLIS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a4dad8b0a34c4ce2d590404bf6df63e1 = L.circleMarker(
                [-22.3581, -47.3831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_18dd92f0fb1377d06b6ce36f452f116d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_560ee7a0806e6b7e6bf6dc51a6589d01 = $(`<div id="html_560ee7a0806e6b7e6bf6dc51a6589d01" style="width: 100.0%; height: 100.0%;"><b>ARARAS (SP)</b><br>3 registros</div>`)[0];
                popup_18dd92f0fb1377d06b6ce36f452f116d.setContent(html_560ee7a0806e6b7e6bf6dc51a6589d01);
            
        

        circle_marker_a4dad8b0a34c4ce2d590404bf6df63e1.bindPopup(popup_18dd92f0fb1377d06b6ce36f452f116d)
        ;

        
    
    
            circle_marker_a4dad8b0a34c4ce2d590404bf6df63e1.bindTooltip(
                `<div>
                     ARARAS: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_10f5c920f8db422c6e2a5ac343482983 = L.circleMarker(
                [-25.4284, -49.2733],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_3065912de6ccf7cb5fd4202ed53cb09a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d4e1fab3db91cd4441a02ed908e65c2d = $(`<div id="html_d4e1fab3db91cd4441a02ed908e65c2d" style="width: 100.0%; height: 100.0%;"><b>CURITIBA (PR)</b><br>2 registros</div>`)[0];
                popup_3065912de6ccf7cb5fd4202ed53cb09a.setContent(html_d4e1fab3db91cd4441a02ed908e65c2d);
            
        

        circle_marker_10f5c920f8db422c6e2a5ac343482983.bindPopup(popup_3065912de6ccf7cb5fd4202ed53cb09a)
        ;

        
    
    
            circle_marker_10f5c920f8db422c6e2a5ac343482983.bindTooltip(
                `<div>
                     CURITIBA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4058f49dc740803fda907c56b9aab098 = L.circleMarker(
                [-23.1864, -46.8842],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_fa2b944b9f76b899e08f11edfe38d7f3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8c5b889be37c49309527de5132dc893d = $(`<div id="html_8c5b889be37c49309527de5132dc893d" style="width: 100.0%; height: 100.0%;"><b>JUNDIAI (SP)</b><br>5 registros</div>`)[0];
                popup_fa2b944b9f76b899e08f11edfe38d7f3.setContent(html_8c5b889be37c49309527de5132dc893d);
            
        

        circle_marker_4058f49dc740803fda907c56b9aab098.bindPopup(popup_fa2b944b9f76b899e08f11edfe38d7f3)
        ;

        
    
    
            circle_marker_4058f49dc740803fda907c56b9aab098.bindTooltip(
                `<div>
                     JUNDIAI: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d26bf8fae5004f7291a4e7b728a457da = L.circleMarker(
                [-23.8131, -47.7131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_21eb2bb85b0f0c6dc5894910eb0580bc = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_409996198a1431b49a6c612a2a4c71b8 = $(`<div id="html_409996198a1431b49a6c612a2a4c71b8" style="width: 100.0%; height: 100.0%;"><b>PILAR DO SUL (SP)</b><br>1 registros</div>`)[0];
                popup_21eb2bb85b0f0c6dc5894910eb0580bc.setContent(html_409996198a1431b49a6c612a2a4c71b8);
            
        

        circle_marker_d26bf8fae5004f7291a4e7b728a457da.bindPopup(popup_21eb2bb85b0f0c6dc5894910eb0580bc)
        ;

        
    
    
            circle_marker_d26bf8fae5004f7291a4e7b728a457da.bindTooltip(
                `<div>
                     PILAR DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3bb4d618562e9fb7b27dca34a4ce83e3 = L.circleMarker(
                [-23.0381, -47.8381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_664fe91b8369435624634bbbedbc3ae5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_15ba827850b81ed04a059500c8ed2190 = $(`<div id="html_15ba827850b81ed04a059500c8ed2190" style="width: 100.0%; height: 100.0%;"><b>LARANJAL PAULISTA (SP)</b><br>1 registros</div>`)[0];
                popup_664fe91b8369435624634bbbedbc3ae5.setContent(html_15ba827850b81ed04a059500c8ed2190);
            
        

        circle_marker_3bb4d618562e9fb7b27dca34a4ce83e3.bindPopup(popup_664fe91b8369435624634bbbedbc3ae5)
        ;

        
    
    
            circle_marker_3bb4d618562e9fb7b27dca34a4ce83e3.bindTooltip(
                `<div>
                     LARANJAL PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_bb1c16fc819853bcba09b8f65634e008 = L.circleMarker(
                [-23.5106, -46.8761],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8636d5dc48fe1d1d73002444d94ee669 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cefbdda071284524e66138aede3d0e9c = $(`<div id="html_cefbdda071284524e66138aede3d0e9c" style="width: 100.0%; height: 100.0%;"><b>BARUERI (SP)</b><br>1 registros</div>`)[0];
                popup_8636d5dc48fe1d1d73002444d94ee669.setContent(html_cefbdda071284524e66138aede3d0e9c);
            
        

        circle_marker_bb1c16fc819853bcba09b8f65634e008.bindPopup(popup_8636d5dc48fe1d1d73002444d94ee669)
        ;

        
    
    
            circle_marker_bb1c16fc819853bcba09b8f65634e008.bindTooltip(
                `<div>
                     BARUERI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8ec7c30e02f0b403eb9ebabc3e829229 = L.circleMarker(
                [-20.8881, -47.5881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_888de483401a08ebe313e16ebeae911f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1b8975dbd22b2b555d0f3444a1b4e308 = $(`<div id="html_1b8975dbd22b2b555d0f3444a1b4e308" style="width: 100.0%; height: 100.0%;"><b>BATATAIS (SP)</b><br>1 registros</div>`)[0];
                popup_888de483401a08ebe313e16ebeae911f.setContent(html_1b8975dbd22b2b555d0f3444a1b4e308);
            
        

        circle_marker_8ec7c30e02f0b403eb9ebabc3e829229.bindPopup(popup_888de483401a08ebe313e16ebeae911f)
        ;

        
    
    
            circle_marker_8ec7c30e02f0b403eb9ebabc3e829229.bindTooltip(
                `<div>
                     BATATAIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_fcce67a117fcc961d8feac5acdd893fe = L.circleMarker(
                [-21.0531, -49.6881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_e44db5a5a4e1262945d88760b070e24a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0c5d1677d29419e08f216a2c84f6b74a = $(`<div id="html_0c5d1677d29419e08f216a2c84f6b74a" style="width: 100.0%; height: 100.0%;"><b>JOSE BONIFACIO (SP)</b><br>1 registros</div>`)[0];
                popup_e44db5a5a4e1262945d88760b070e24a.setContent(html_0c5d1677d29419e08f216a2c84f6b74a);
            
        

        circle_marker_fcce67a117fcc961d8feac5acdd893fe.bindPopup(popup_e44db5a5a4e1262945d88760b070e24a)
        ;

        
    
    
            circle_marker_fcce67a117fcc961d8feac5acdd893fe.bindTooltip(
                `<div>
                     JOSE BONIFACIO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1147025436a29c9a0886c1b47fe63e4b = L.circleMarker(
                [-20.5386, -47.4006],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_d5f59fd71efd368d620a10c6e1439a9c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f1468c0e6ed8492dfa834666272f91ec = $(`<div id="html_f1468c0e6ed8492dfa834666272f91ec" style="width: 100.0%; height: 100.0%;"><b>FRANCA (SP)</b><br>2 registros</div>`)[0];
                popup_d5f59fd71efd368d620a10c6e1439a9c.setContent(html_f1468c0e6ed8492dfa834666272f91ec);
            
        

        circle_marker_1147025436a29c9a0886c1b47fe63e4b.bindPopup(popup_d5f59fd71efd368d620a10c6e1439a9c)
        ;

        
    
    
            circle_marker_1147025436a29c9a0886c1b47fe63e4b.bindTooltip(
                `<div>
                     FRANCA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_678dc5729125d26a32dab98c3cfd443f = L.circleMarker(
                [-22.5647, -47.4017],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_399cebb107869e882d130025fa97798e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4fd8eb31abe45ac05facbb28759fac4d = $(`<div id="html_4fd8eb31abe45ac05facbb28759fac4d" style="width: 100.0%; height: 100.0%;"><b>LIMEIRA (SP)</b><br>1 registros</div>`)[0];
                popup_399cebb107869e882d130025fa97798e.setContent(html_4fd8eb31abe45ac05facbb28759fac4d);
            
        

        circle_marker_678dc5729125d26a32dab98c3cfd443f.bindPopup(popup_399cebb107869e882d130025fa97798e)
        ;

        
    
    
            circle_marker_678dc5729125d26a32dab98c3cfd443f.bindTooltip(
                `<div>
                     LIMEIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4f0881c9ef0672571b20308d0aa78b7e = L.circleMarker(
                [-22.7031, -46.9881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8e7be695045a87f7ff74de5542da6502 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_91f741466c627addea2ad3c94f4bb0ef = $(`<div id="html_91f741466c627addea2ad3c94f4bb0ef" style="width: 100.0%; height: 100.0%;"><b>JAGUARIUNA (SP)</b><br>1 registros</div>`)[0];
                popup_8e7be695045a87f7ff74de5542da6502.setContent(html_91f741466c627addea2ad3c94f4bb0ef);
            
        

        circle_marker_4f0881c9ef0672571b20308d0aa78b7e.bindPopup(popup_8e7be695045a87f7ff74de5542da6502)
        ;

        
    
    
            circle_marker_4f0881c9ef0672571b20308d0aa78b7e.bindTooltip(
                `<div>
                     JAGUARIUNA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_db5ee3e6ebc952472f3346a76dd5af5a = L.circleMarker(
                [-23.6629, -46.5383],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_105b97438b9db75da66f3aeb4095723a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f7b9ad028ff477c5f14cdb076ac19629 = $(`<div id="html_f7b9ad028ff477c5f14cdb076ac19629" style="width: 100.0%; height: 100.0%;"><b>SANTO ANDRE (SP)</b><br>1 registros</div>`)[0];
                popup_105b97438b9db75da66f3aeb4095723a.setContent(html_f7b9ad028ff477c5f14cdb076ac19629);
            
        

        circle_marker_db5ee3e6ebc952472f3346a76dd5af5a.bindPopup(popup_105b97438b9db75da66f3aeb4095723a)
        ;

        
    
    
            circle_marker_db5ee3e6ebc952472f3346a76dd5af5a.bindTooltip(
                `<div>
                     SANTO ANDRE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d9a07c78eea6b7f342bf95e69fcaccc3 = L.circleMarker(
                [-26.3044, -48.8456],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_022f167885f990d2d71d3f5c231b7117 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9e84257bf06aebdffcbaafb7aaec15c2 = $(`<div id="html_9e84257bf06aebdffcbaafb7aaec15c2" style="width: 100.0%; height: 100.0%;"><b>JOINVILLE (SC)</b><br>5 registros</div>`)[0];
                popup_022f167885f990d2d71d3f5c231b7117.setContent(html_9e84257bf06aebdffcbaafb7aaec15c2);
            
        

        circle_marker_d9a07c78eea6b7f342bf95e69fcaccc3.bindPopup(popup_022f167885f990d2d71d3f5c231b7117)
        ;

        
    
    
            circle_marker_d9a07c78eea6b7f342bf95e69fcaccc3.bindTooltip(
                `<div>
                     JOINVILLE: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_420baff89221d051355c6bd0e21727cc = L.circleMarker(
                [-23.1794, -45.8869],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 21, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_5b28146cdc6b47882ac4b9ee843599ef = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f659e3007f9155688e055f88e9aec899 = $(`<div id="html_f659e3007f9155688e055f88e9aec899" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DOS CAMPOS (SP)</b><br>7 registros</div>`)[0];
                popup_5b28146cdc6b47882ac4b9ee843599ef.setContent(html_f659e3007f9155688e055f88e9aec899);
            
        

        circle_marker_420baff89221d051355c6bd0e21727cc.bindPopup(popup_5b28146cdc6b47882ac4b9ee843599ef)
        ;

        
    
    
            circle_marker_420baff89221d051355c6bd0e21727cc.bindTooltip(
                `<div>
                     SAO JOSE DOS CAMPOS: 7
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0c0fe3472a1e2d692372d38bfd2a0ea9 = L.circleMarker(
                [-22.4114, -47.5614],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_83dfe5f8770c3917fa71755389cfa878 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f1b21d2a6ce80097ff3b7c8c3589dc8a = $(`<div id="html_f1b21d2a6ce80097ff3b7c8c3589dc8a" style="width: 100.0%; height: 100.0%;"><b>RIO CLARO (SP)</b><br>2 registros</div>`)[0];
                popup_83dfe5f8770c3917fa71755389cfa878.setContent(html_f1b21d2a6ce80097ff3b7c8c3589dc8a);
            
        

        circle_marker_0c0fe3472a1e2d692372d38bfd2a0ea9.bindPopup(popup_83dfe5f8770c3917fa71755389cfa878)
        ;

        
    
    
            circle_marker_0c0fe3472a1e2d692372d38bfd2a0ea9.bindTooltip(
                `<div>
                     RIO CLARO: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2f9b8fdd154ec6f05bf438d1ac3a7e9c = L.circleMarker(
                [-23.5225, -46.1883],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_24efead046b785dd32b6e34ae3e4da29 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b5867fb2d425562b249c82a28379b3dd = $(`<div id="html_b5867fb2d425562b249c82a28379b3dd" style="width: 100.0%; height: 100.0%;"><b>MOJI DAS CRUZES (SP)</b><br>1 registros</div>`)[0];
                popup_24efead046b785dd32b6e34ae3e4da29.setContent(html_b5867fb2d425562b249c82a28379b3dd);
            
        

        circle_marker_2f9b8fdd154ec6f05bf438d1ac3a7e9c.bindPopup(popup_24efead046b785dd32b6e34ae3e4da29)
        ;

        
    
    
            circle_marker_2f9b8fdd154ec6f05bf438d1ac3a7e9c.bindTooltip(
                `<div>
                     MOJI DAS CRUZES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f99b10ae359e5463e9b2b1a187e542de = L.circleMarker(
                [-24.1831, -46.7881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_1781527023ede7b6b70d7e27b942c6a1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d3c0630d0f67b1ade782be84694c8b4c = $(`<div id="html_d3c0630d0f67b1ade782be84694c8b4c" style="width: 100.0%; height: 100.0%;"><b>ITANHAEM (SP)</b><br>1 registros</div>`)[0];
                popup_1781527023ede7b6b70d7e27b942c6a1.setContent(html_d3c0630d0f67b1ade782be84694c8b4c);
            
        

        circle_marker_f99b10ae359e5463e9b2b1a187e542de.bindPopup(popup_1781527023ede7b6b70d7e27b942c6a1)
        ;

        
    
    
            circle_marker_f99b10ae359e5463e9b2b1a187e542de.bindTooltip(
                `<div>
                     ITANHAEM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d63ca9daeea4c6c09fc170f06fd3329b = L.circleMarker(
                [-21.1775, -47.8103],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_9ecd01647edf331a182656d0e61d78bb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0cbb49f02cb69b6b7e11c202e00feecd = $(`<div id="html_0cbb49f02cb69b6b7e11c202e00feecd" style="width: 100.0%; height: 100.0%;"><b>RIBEIRAO PRETO (SP)</b><br>2 registros</div>`)[0];
                popup_9ecd01647edf331a182656d0e61d78bb.setContent(html_0cbb49f02cb69b6b7e11c202e00feecd);
            
        

        circle_marker_d63ca9daeea4c6c09fc170f06fd3329b.bindPopup(popup_9ecd01647edf331a182656d0e61d78bb)
        ;

        
    
    
            circle_marker_d63ca9daeea4c6c09fc170f06fd3329b.bindTooltip(
                `<div>
                     RIBEIRAO PRETO: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_020c902693561108813a796f1b1a3b4f = L.circleMarker(
                [-20.8197, -49.3794],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_cec1d1b53aa47dbf6b9a7a17488b6a96 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3403ff15fd9e9d928dd8f1db6bf07493 = $(`<div id="html_3403ff15fd9e9d928dd8f1db6bf07493" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DO RIO PRETO (SP)</b><br>3 registros</div>`)[0];
                popup_cec1d1b53aa47dbf6b9a7a17488b6a96.setContent(html_3403ff15fd9e9d928dd8f1db6bf07493);
            
        

        circle_marker_020c902693561108813a796f1b1a3b4f.bindPopup(popup_cec1d1b53aa47dbf6b9a7a17488b6a96)
        ;

        
    
    
            circle_marker_020c902693561108813a796f1b1a3b4f.bindTooltip(
                `<div>
                     SAO JOSE DO RIO PRETO: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_573e618e3b2a4d336303d17d310066d0 = L.circleMarker(
                [-23.9608, -46.3331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_4208e6087daa9b85ab0f349c8746bf6f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c73d9544195905f4a429b7733d6b1eb8 = $(`<div id="html_c73d9544195905f4a429b7733d6b1eb8" style="width: 100.0%; height: 100.0%;"><b>SANTOS (SP)</b><br>3 registros</div>`)[0];
                popup_4208e6087daa9b85ab0f349c8746bf6f.setContent(html_c73d9544195905f4a429b7733d6b1eb8);
            
        

        circle_marker_573e618e3b2a4d336303d17d310066d0.bindPopup(popup_4208e6087daa9b85ab0f349c8746bf6f)
        ;

        
    
    
            circle_marker_573e618e3b2a4d336303d17d310066d0.bindTooltip(
                `<div>
                     SANTOS: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_dd08229fc66411d36599456c49e04a27 = L.circleMarker(
                [-24.0931, -46.6181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_0b3b02adcb02ca6881bee7e89f6afd8f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_36732facc66b1fd2041429fd2fb67080 = $(`<div id="html_36732facc66b1fd2041429fd2fb67080" style="width: 100.0%; height: 100.0%;"><b>MONGAGUA (SP)</b><br>3 registros</div>`)[0];
                popup_0b3b02adcb02ca6881bee7e89f6afd8f.setContent(html_36732facc66b1fd2041429fd2fb67080);
            
        

        circle_marker_dd08229fc66411d36599456c49e04a27.bindPopup(popup_0b3b02adcb02ca6881bee7e89f6afd8f)
        ;

        
    
    
            circle_marker_dd08229fc66411d36599456c49e04a27.bindTooltip(
                `<div>
                     MONGAGUA: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b2bb3970fdde4a185743aa9871a30927 = L.circleMarker(
                [-24.3181, -46.9981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_7f11414fcf56d93e0b46c2ae17ac025f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ee6ec9768c43f20e2c4743e84e4e9b40 = $(`<div id="html_ee6ec9768c43f20e2c4743e84e4e9b40" style="width: 100.0%; height: 100.0%;"><b>PERUIBE (SP)</b><br>2 registros</div>`)[0];
                popup_7f11414fcf56d93e0b46c2ae17ac025f.setContent(html_ee6ec9768c43f20e2c4743e84e4e9b40);
            
        

        circle_marker_b2bb3970fdde4a185743aa9871a30927.bindPopup(popup_7f11414fcf56d93e0b46c2ae17ac025f)
        ;

        
    
    
            circle_marker_b2bb3970fdde4a185743aa9871a30927.bindTooltip(
                `<div>
                     PERUIBE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d7d64ac9d35b22acb362fbfae83c14c4 = L.circleMarker(
                [-20.7181, -47.8881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_bed2068b2aa4988536d22d8415f41b0c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0e1ae272713147ebf9ab8608752eb7fc = $(`<div id="html_0e1ae272713147ebf9ab8608752eb7fc" style="width: 100.0%; height: 100.0%;"><b>ORLANDIA (SP)</b><br>1 registros</div>`)[0];
                popup_bed2068b2aa4988536d22d8415f41b0c.setContent(html_0e1ae272713147ebf9ab8608752eb7fc);
            
        

        circle_marker_d7d64ac9d35b22acb362fbfae83c14c4.bindPopup(popup_bed2068b2aa4988536d22d8415f41b0c)
        ;

        
    
    
            circle_marker_d7d64ac9d35b22acb362fbfae83c14c4.bindTooltip(
                `<div>
                     ORLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d25fb60d634a69483473d9776b61aa75 = L.circleMarker(
                [-22.8219, -47.2669],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8db2d67ca23939b66bca361fdab387b7 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a1aba00f03dc498e6595d86352751eda = $(`<div id="html_a1aba00f03dc498e6595d86352751eda" style="width: 100.0%; height: 100.0%;"><b>SUMARE (SP)</b><br>2 registros</div>`)[0];
                popup_8db2d67ca23939b66bca361fdab387b7.setContent(html_a1aba00f03dc498e6595d86352751eda);
            
        

        circle_marker_d25fb60d634a69483473d9776b61aa75.bindPopup(popup_8db2d67ca23939b66bca361fdab387b7)
        ;

        
    
    
            circle_marker_d25fb60d634a69483473d9776b61aa75.bindTooltip(
                `<div>
                     SUMARE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_18ea91ade65f3afead0ffae2581658b1 = L.circleMarker(
                [-21.5931, -46.8931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_290db5ff2b2c460e38787457393dc2a1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_25faee1268310a3de362b839c14f47a4 = $(`<div id="html_25faee1268310a3de362b839c14f47a4" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DO RIO PARDO (SP)</b><br>1 registros</div>`)[0];
                popup_290db5ff2b2c460e38787457393dc2a1.setContent(html_25faee1268310a3de362b839c14f47a4);
            
        

        circle_marker_18ea91ade65f3afead0ffae2581658b1.bindPopup(popup_290db5ff2b2c460e38787457393dc2a1)
        ;

        
    
    
            circle_marker_18ea91ade65f3afead0ffae2581658b1.bindTooltip(
                `<div>
                     SAO JOSE DO RIO PARDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_72781e5eb9266afcae004bd3bf57ca82 = L.circleMarker(
                [-24.1581, -49.8231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_78e879b85701b002c0a88516eed5969e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_07753426ce1e5e4e1ea3af9420de8618 = $(`<div id="html_07753426ce1e5e4e1ea3af9420de8618" style="width: 100.0%; height: 100.0%;"><b>ARAPOTI (PR)</b><br>1 registros</div>`)[0];
                popup_78e879b85701b002c0a88516eed5969e.setContent(html_07753426ce1e5e4e1ea3af9420de8618);
            
        

        circle_marker_72781e5eb9266afcae004bd3bf57ca82.bindPopup(popup_78e879b85701b002c0a88516eed5969e)
        ;

        
    
    
            circle_marker_72781e5eb9266afcae004bd3bf57ca82.bindTooltip(
                `<div>
                     ARAPOTI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ec44f56f7d497db823b92bbe04008626 = L.circleMarker(
                [-27.2142, -49.6431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a76c07166acc2ed992023f2b9a915b02 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3ec5730e699aa1018368eb2e04bc52f6 = $(`<div id="html_3ec5730e699aa1018368eb2e04bc52f6" style="width: 100.0%; height: 100.0%;"><b>RIO DO SUL (SC)</b><br>2 registros</div>`)[0];
                popup_a76c07166acc2ed992023f2b9a915b02.setContent(html_3ec5730e699aa1018368eb2e04bc52f6);
            
        

        circle_marker_ec44f56f7d497db823b92bbe04008626.bindPopup(popup_a76c07166acc2ed992023f2b9a915b02)
        ;

        
    
    
            circle_marker_ec44f56f7d497db823b92bbe04008626.bindTooltip(
                `<div>
                     RIO DO SUL: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b99f362377bcafbdfc33efcd34b6b4f8 = L.circleMarker(
                [-23.7181, -46.8481],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_cd7f53226dfe42c25ddadcf22607b966 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f75eeb3d8243784ada6844fb6b2c4072 = $(`<div id="html_f75eeb3d8243784ada6844fb6b2c4072" style="width: 100.0%; height: 100.0%;"><b>ITAPECERICA DA SERRA (SP)</b><br>1 registros</div>`)[0];
                popup_cd7f53226dfe42c25ddadcf22607b966.setContent(html_f75eeb3d8243784ada6844fb6b2c4072);
            
        

        circle_marker_b99f362377bcafbdfc33efcd34b6b4f8.bindPopup(popup_cd7f53226dfe42c25ddadcf22607b966)
        ;

        
    
    
            circle_marker_b99f362377bcafbdfc33efcd34b6b4f8.bindTooltip(
                `<div>
                     ITAPECERICA DA SERRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7b3a0761ff636c2454b0d2f0b798ecf2 = L.circleMarker(
                [-23.3053, -45.9658],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_87ff795cb9c1d4d0d0a4f75b33a3e231 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_67f151f716c9e6079dcd30db0796ad79 = $(`<div id="html_67f151f716c9e6079dcd30db0796ad79" style="width: 100.0%; height: 100.0%;"><b>JACAREI (SP)</b><br>2 registros</div>`)[0];
                popup_87ff795cb9c1d4d0d0a4f75b33a3e231.setContent(html_67f151f716c9e6079dcd30db0796ad79);
            
        

        circle_marker_7b3a0761ff636c2454b0d2f0b798ecf2.bindPopup(popup_87ff795cb9c1d4d0d0a4f75b33a3e231)
        ;

        
    
    
            circle_marker_7b3a0761ff636c2454b0d2f0b798ecf2.bindTooltip(
                `<div>
                     JACAREI: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4f8b37c47ab907cc7a28fe4da3b6b047 = L.circleMarker(
                [-22.9781, -49.8731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a5a1f0846080b9c0f9f2ad77324e2c4c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_68a831769849f8ce79127e43b9b5d627 = $(`<div id="html_68a831769849f8ce79127e43b9b5d627" style="width: 100.0%; height: 100.0%;"><b>OURINHOS (SP)</b><br>2 registros</div>`)[0];
                popup_a5a1f0846080b9c0f9f2ad77324e2c4c.setContent(html_68a831769849f8ce79127e43b9b5d627);
            
        

        circle_marker_4f8b37c47ab907cc7a28fe4da3b6b047.bindPopup(popup_a5a1f0846080b9c0f9f2ad77324e2c4c)
        ;

        
    
    
            circle_marker_4f8b37c47ab907cc7a28fe4da3b6b047.bindTooltip(
                `<div>
                     OURINHOS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_61fb87ccf84674db2db8adb5da797708 = L.circleMarker(
                [-22.1831, -47.3931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_83c2707477f5d5f2b91057a16566d790 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_be928c49c92af2bcd18c92dbfd89fc39 = $(`<div id="html_be928c49c92af2bcd18c92dbfd89fc39" style="width: 100.0%; height: 100.0%;"><b>LEME (SP)</b><br>1 registros</div>`)[0];
                popup_83c2707477f5d5f2b91057a16566d790.setContent(html_be928c49c92af2bcd18c92dbfd89fc39);
            
        

        circle_marker_61fb87ccf84674db2db8adb5da797708.bindPopup(popup_83c2707477f5d5f2b91057a16566d790)
        ;

        
    
    
            circle_marker_61fb87ccf84674db2db8adb5da797708.bindTooltip(
                `<div>
                     LEME: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b3d2a59662388ac9ae78cb848bc772af = L.circleMarker(
                [-20.4711, -45.9564],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_f680b131e78e22249ceb7123a74a1fca = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_58ec62df296b36b8696e0a6496c82edc = $(`<div id="html_58ec62df296b36b8696e0a6496c82edc" style="width: 100.0%; height: 100.0%;"><b>PIUMHI (MG)</b><br>1 registros</div>`)[0];
                popup_f680b131e78e22249ceb7123a74a1fca.setContent(html_58ec62df296b36b8696e0a6496c82edc);
            
        

        circle_marker_b3d2a59662388ac9ae78cb848bc772af.bindPopup(popup_f680b131e78e22249ceb7123a74a1fca)
        ;

        
    
    
            circle_marker_b3d2a59662388ac9ae78cb848bc772af.bindTooltip(
                `<div>
                     PIUMHI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f26463f7e2da79ec871c40191cd27b6a = L.circleMarker(
                [-23.3045, -51.1696],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_4fc22ff688f9916bc53f332a981b5aab = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_596fc9b7c3bca499b98b150f3be74591 = $(`<div id="html_596fc9b7c3bca499b98b150f3be74591" style="width: 100.0%; height: 100.0%;"><b>LONDRINA (PR)</b><br>1 registros</div>`)[0];
                popup_4fc22ff688f9916bc53f332a981b5aab.setContent(html_596fc9b7c3bca499b98b150f3be74591);
            
        

        circle_marker_f26463f7e2da79ec871c40191cd27b6a.bindPopup(popup_4fc22ff688f9916bc53f332a981b5aab)
        ;

        
    
    
            circle_marker_f26463f7e2da79ec871c40191cd27b6a.bindTooltip(
                `<div>
                     LONDRINA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8cbdedc36bd023f72263ea1470093e11 = L.circleMarker(
                [-23.2281, -47.9531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_286120500c523d6f6a5314a913a337a0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e4f3ca4d5ff7a2952bb76ad03b9ae31c = $(`<div id="html_e4f3ca4d5ff7a2952bb76ad03b9ae31c" style="width: 100.0%; height: 100.0%;"><b>CESARIO LANGE (SP)</b><br>1 registros</div>`)[0];
                popup_286120500c523d6f6a5314a913a337a0.setContent(html_e4f3ca4d5ff7a2952bb76ad03b9ae31c);
            
        

        circle_marker_8cbdedc36bd023f72263ea1470093e11.bindPopup(popup_286120500c523d6f6a5314a913a337a0)
        ;

        
    
    
            circle_marker_8cbdedc36bd023f72263ea1470093e11.bindTooltip(
                `<div>
                     CESARIO LANGE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7d9b1bfa74d3b5911a50275e78e10fab = L.circleMarker(
                [-22.3711, -41.7869],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_18f26fbc8f28666993f3c10d537d3127 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9e87659022a98a718483bfba84a7ddda = $(`<div id="html_9e87659022a98a718483bfba84a7ddda" style="width: 100.0%; height: 100.0%;"><b>MACAE (RJ)</b><br>2 registros</div>`)[0];
                popup_18f26fbc8f28666993f3c10d537d3127.setContent(html_9e87659022a98a718483bfba84a7ddda);
            
        

        circle_marker_7d9b1bfa74d3b5911a50275e78e10fab.bindPopup(popup_18f26fbc8f28666993f3c10d537d3127)
        ;

        
    
    
            circle_marker_7d9b1bfa74d3b5911a50275e78e10fab.bindTooltip(
                `<div>
                     MACAE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6a00852830f85b0842cddcba70089c27 = L.circleMarker(
                [-23.0264, -45.5556],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_f957895762b615e8ebf9465a2f8a9419 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_37575890114c199f16916ea41e149dce = $(`<div id="html_37575890114c199f16916ea41e149dce" style="width: 100.0%; height: 100.0%;"><b>TAUBATE (SP)</b><br>2 registros</div>`)[0];
                popup_f957895762b615e8ebf9465a2f8a9419.setContent(html_37575890114c199f16916ea41e149dce);
            
        

        circle_marker_6a00852830f85b0842cddcba70089c27.bindPopup(popup_f957895762b615e8ebf9465a2f8a9419)
        ;

        
    
    
            circle_marker_6a00852830f85b0842cddcba70089c27.bindTooltip(
                `<div>
                     TAUBATE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1b4b4cb8a993d5d8480d449f48d921c6 = L.circleMarker(
                [-22.3208, -49.0608],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_1695e392ab2fc9f699f31ca7d7436316 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_25b78f92ba57fb907ca0bc78b83c34b0 = $(`<div id="html_25b78f92ba57fb907ca0bc78b83c34b0" style="width: 100.0%; height: 100.0%;"><b>BAURU (SP)</b><br>3 registros</div>`)[0];
                popup_1695e392ab2fc9f699f31ca7d7436316.setContent(html_25b78f92ba57fb907ca0bc78b83c34b0);
            
        

        circle_marker_1b4b4cb8a993d5d8480d449f48d921c6.bindPopup(popup_1695e392ab2fc9f699f31ca7d7436316)
        ;

        
    
    
            circle_marker_1b4b4cb8a993d5d8480d449f48d921c6.bindTooltip(
                `<div>
                     BAURU: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_41672e6080d22c6d50422d7c7947e88f = L.circleMarker(
                [-22.5731, -44.9631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_396eccdeae48516b133ea83d4f074738 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0335aadd00caf6bebed2960aa27e5126 = $(`<div id="html_0335aadd00caf6bebed2960aa27e5126" style="width: 100.0%; height: 100.0%;"><b>CRUZEIRO (SP)</b><br>1 registros</div>`)[0];
                popup_396eccdeae48516b133ea83d4f074738.setContent(html_0335aadd00caf6bebed2960aa27e5126);
            
        

        circle_marker_41672e6080d22c6d50422d7c7947e88f.bindPopup(popup_396eccdeae48516b133ea83d4f074738)
        ;

        
    
    
            circle_marker_41672e6080d22c6d50422d7c7947e88f.bindTooltip(
                `<div>
                     CRUZEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9b2cc04faf8fa1f79c8da9a9c921bbad = L.circleMarker(
                [-18.9113, -48.2622],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_c815784c58983f7f6e995494835555bc = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9783e44cda9fe9396269f39dbbbf6e82 = $(`<div id="html_9783e44cda9fe9396269f39dbbbf6e82" style="width: 100.0%; height: 100.0%;"><b>UBERLANDIA (MG)</b><br>1 registros</div>`)[0];
                popup_c815784c58983f7f6e995494835555bc.setContent(html_9783e44cda9fe9396269f39dbbbf6e82);
            
        

        circle_marker_9b2cc04faf8fa1f79c8da9a9c921bbad.bindPopup(popup_c815784c58983f7f6e995494835555bc)
        ;

        
    
    
            circle_marker_9b2cc04faf8fa1f79c8da9a9c921bbad.bindTooltip(
                `<div>
                     UBERLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c73aee1e0c63bd3e389ae839d8758f17 = L.circleMarker(
                [-23.8531, -46.1381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a6fba17d5c0f1539dbe9f2f876382b7d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3a98a756c8852caba0013272909b56c2 = $(`<div id="html_3a98a756c8852caba0013272909b56c2" style="width: 100.0%; height: 100.0%;"><b>BERTIOGA (SP)</b><br>1 registros</div>`)[0];
                popup_a6fba17d5c0f1539dbe9f2f876382b7d.setContent(html_3a98a756c8852caba0013272909b56c2);
            
        

        circle_marker_c73aee1e0c63bd3e389ae839d8758f17.bindPopup(popup_a6fba17d5c0f1539dbe9f2f876382b7d)
        ;

        
    
    
            circle_marker_c73aee1e0c63bd3e389ae839d8758f17.bindTooltip(
                `<div>
                     BERTIOGA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_34408aec6f8a1a580765cd87c9e81a3a = L.circleMarker(
                [-18.9431, -46.9931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_44e60ae57e29fb5baae8f299717aa4fb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a36a02f271fd3d7523df2766e389b07f = $(`<div id="html_a36a02f271fd3d7523df2766e389b07f" style="width: 100.0%; height: 100.0%;"><b>PATROCINIO (MG)</b><br>1 registros</div>`)[0];
                popup_44e60ae57e29fb5baae8f299717aa4fb.setContent(html_a36a02f271fd3d7523df2766e389b07f);
            
        

        circle_marker_34408aec6f8a1a580765cd87c9e81a3a.bindPopup(popup_44e60ae57e29fb5baae8f299717aa4fb)
        ;

        
    
    
            circle_marker_34408aec6f8a1a580765cd87c9e81a3a.bindTooltip(
                `<div>
                     PATROCINIO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_056d921b2e3eddf8a19fad93dc0ee06c = L.circleMarker(
                [-23.1531, -47.0631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_53f296ac43e4c0724c127f5a46314ea1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e9fd3bcd8d56ad53841b9cfc84628095 = $(`<div id="html_e9fd3bcd8d56ad53841b9cfc84628095" style="width: 100.0%; height: 100.0%;"><b>ITUPEVA (SP)</b><br>1 registros</div>`)[0];
                popup_53f296ac43e4c0724c127f5a46314ea1.setContent(html_e9fd3bcd8d56ad53841b9cfc84628095);
            
        

        circle_marker_056d921b2e3eddf8a19fad93dc0ee06c.bindPopup(popup_53f296ac43e4c0724c127f5a46314ea1)
        ;

        
    
    
            circle_marker_056d921b2e3eddf8a19fad93dc0ee06c.bindTooltip(
                `<div>
                     ITUPEVA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_12b318f679d23b5a13969fbf0300cc28 = L.circleMarker(
                [-23.0881, -46.9431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_5e8bc0021b65eab4a92a72f54206f7ee = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a95d6e94a971540b6dc974c3960275b8 = $(`<div id="html_a95d6e94a971540b6dc974c3960275b8" style="width: 100.0%; height: 100.0%;"><b>LOUVEIRA (SP)</b><br>1 registros</div>`)[0];
                popup_5e8bc0021b65eab4a92a72f54206f7ee.setContent(html_a95d6e94a971540b6dc974c3960275b8);
            
        

        circle_marker_12b318f679d23b5a13969fbf0300cc28.bindPopup(popup_5e8bc0021b65eab4a92a72f54206f7ee)
        ;

        
    
    
            circle_marker_12b318f679d23b5a13969fbf0300cc28.bindTooltip(
                `<div>
                     LOUVEIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0cf41709a65dd5d80331d3bbc6d00332 = L.circleMarker(
                [-23.7781, -45.3581],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_eb47f2815076ef16b3587e0b9cd582dc = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_207f7eb89ee76ede3033b990d4be82f4 = $(`<div id="html_207f7eb89ee76ede3033b990d4be82f4" style="width: 100.0%; height: 100.0%;"><b>ILHABELA (SP)</b><br>1 registros</div>`)[0];
                popup_eb47f2815076ef16b3587e0b9cd582dc.setContent(html_207f7eb89ee76ede3033b990d4be82f4);
            
        

        circle_marker_0cf41709a65dd5d80331d3bbc6d00332.bindPopup(popup_eb47f2815076ef16b3587e0b9cd582dc)
        ;

        
    
    
            circle_marker_0cf41709a65dd5d80331d3bbc6d00332.bindTooltip(
                `<div>
                     ILHABELA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_fb6d56995c975b1f2bbdfddd747692a9 = L.circleMarker(
                [-23.3631, -46.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_2e9e7af93e8dea2c5abcc55cdd953d9b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a1f1abf2019e37bb7010f4911378cdce = $(`<div id="html_a1f1abf2019e37bb7010f4911378cdce" style="width: 100.0%; height: 100.0%;"><b>CAIEIRAS (SP)</b><br>1 registros</div>`)[0];
                popup_2e9e7af93e8dea2c5abcc55cdd953d9b.setContent(html_a1f1abf2019e37bb7010f4911378cdce);
            
        

        circle_marker_fb6d56995c975b1f2bbdfddd747692a9.bindPopup(popup_2e9e7af93e8dea2c5abcc55cdd953d9b)
        ;

        
    
    
            circle_marker_fb6d56995c975b1f2bbdfddd747692a9.bindTooltip(
                `<div>
                     CAIEIRAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_fa9dff66b983f676a55d8a2f0c6a75dc = L.circleMarker(
                [-21.2731, -47.3031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_cc479226fb92e75e84c4b12b4bc61433 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fd87792b8ae2075e19971957ef13745c = $(`<div id="html_fd87792b8ae2075e19971957ef13745c" style="width: 100.0%; height: 100.0%;"><b>CAJURU (SP)</b><br>1 registros</div>`)[0];
                popup_cc479226fb92e75e84c4b12b4bc61433.setContent(html_fd87792b8ae2075e19971957ef13745c);
            
        

        circle_marker_fa9dff66b983f676a55d8a2f0c6a75dc.bindPopup(popup_cc479226fb92e75e84c4b12b4bc61433)
        ;

        
    
    
            circle_marker_fa9dff66b983f676a55d8a2f0c6a75dc.bindTooltip(
                `<div>
                     CAJURU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d34f8fce912f7a552433f8be562ff878 = L.circleMarker(
                [-22.8481, -45.2331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_d3a61862c332374bfc23170fc68e06b3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d4f505ed9d58ac23492bc87cd9ffbb3f = $(`<div id="html_d4f505ed9d58ac23492bc87cd9ffbb3f" style="width: 100.0%; height: 100.0%;"><b>APARECIDA (SP)</b><br>1 registros</div>`)[0];
                popup_d3a61862c332374bfc23170fc68e06b3.setContent(html_d4f505ed9d58ac23492bc87cd9ffbb3f);
            
        

        circle_marker_d34f8fce912f7a552433f8be562ff878.bindPopup(popup_d3a61862c332374bfc23170fc68e06b3)
        ;

        
    
    
            circle_marker_d34f8fce912f7a552433f8be562ff878.bindTooltip(
                `<div>
                     APARECIDA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f5555fe961fc58724207980ecdcb4be0 = L.circleMarker(
                [-26.7431, -49.1781],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_3d78b2d3e22293c9f4c08b80a9fbd8ee = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_98c6ed60978cfcb77d76417bbc461d84 = $(`<div id="html_98c6ed60978cfcb77d76417bbc461d84" style="width: 100.0%; height: 100.0%;"><b>POMERODE (SC)</b><br>1 registros</div>`)[0];
                popup_3d78b2d3e22293c9f4c08b80a9fbd8ee.setContent(html_98c6ed60978cfcb77d76417bbc461d84);
            
        

        circle_marker_f5555fe961fc58724207980ecdcb4be0.bindPopup(popup_3d78b2d3e22293c9f4c08b80a9fbd8ee)
        ;

        
    
    
            circle_marker_f5555fe961fc58724207980ecdcb4be0.bindTooltip(
                `<div>
                     POMERODE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f381ded92ec8e28ac8f271d6a4aee47f = L.circleMarker(
                [-27.1581, -48.5531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_13232a575bff718aa4e302667078d8f4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b5acdcf3890f3c173f5c0ba2b6e423ce = $(`<div id="html_b5acdcf3890f3c173f5c0ba2b6e423ce" style="width: 100.0%; height: 100.0%;"><b>PORTO BELO (SC)</b><br>1 registros</div>`)[0];
                popup_13232a575bff718aa4e302667078d8f4.setContent(html_b5acdcf3890f3c173f5c0ba2b6e423ce);
            
        

        circle_marker_f381ded92ec8e28ac8f271d6a4aee47f.bindPopup(popup_13232a575bff718aa4e302667078d8f4)
        ;

        
    
    
            circle_marker_f381ded92ec8e28ac8f271d6a4aee47f.bindTooltip(
                `<div>
                     PORTO BELO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_46b7a092a2f19b7602f7c9e4356c508f = L.circleMarker(
                [-23.5489, -46.9342],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_46c4e0e2ec5b41250c0b4f06cb21c99b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e82672cf7e921c62e9bcf0fbaae601c1 = $(`<div id="html_e82672cf7e921c62e9bcf0fbaae601c1" style="width: 100.0%; height: 100.0%;"><b>ITAPEVI (SP)</b><br>1 registros</div>`)[0];
                popup_46c4e0e2ec5b41250c0b4f06cb21c99b.setContent(html_e82672cf7e921c62e9bcf0fbaae601c1);
            
        

        circle_marker_46b7a092a2f19b7602f7c9e4356c508f.bindPopup(popup_46c4e0e2ec5b41250c0b4f06cb21c99b)
        ;

        
    
    
            circle_marker_46b7a092a2f19b7602f7c9e4356c508f.bindTooltip(
                `<div>
                     ITAPEVI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_67c075e8e65feab6edcd5153264701ff = L.circleMarker(
                [-22.7253, -47.6492],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_b585a4020bababb3ff5fe8c78aa20aa3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_55d4d96f2202370e5c108011e157459a = $(`<div id="html_55d4d96f2202370e5c108011e157459a" style="width: 100.0%; height: 100.0%;"><b>PIRACICABA (SP)</b><br>1 registros</div>`)[0];
                popup_b585a4020bababb3ff5fe8c78aa20aa3.setContent(html_55d4d96f2202370e5c108011e157459a);
            
        

        circle_marker_67c075e8e65feab6edcd5153264701ff.bindPopup(popup_b585a4020bababb3ff5fe8c78aa20aa3)
        ;

        
    
    
            circle_marker_67c075e8e65feab6edcd5153264701ff.bindTooltip(
                `<div>
                     PIRACICABA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_90b8e66572848e5a8dbd407839ab4516 = L.circleMarker(
                [-22.8581, -47.2181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_f3266007f80fbd9c667db7bfcd169f9e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4a43e3437e070daedb659af4954f32eb = $(`<div id="html_4a43e3437e070daedb659af4954f32eb" style="width: 100.0%; height: 100.0%;"><b>HORTOLANDIA (SP)</b><br>1 registros</div>`)[0];
                popup_f3266007f80fbd9c667db7bfcd169f9e.setContent(html_4a43e3437e070daedb659af4954f32eb);
            
        

        circle_marker_90b8e66572848e5a8dbd407839ab4516.bindPopup(popup_f3266007f80fbd9c667db7bfcd169f9e)
        ;

        
    
    
            circle_marker_90b8e66572848e5a8dbd407839ab4516.bindTooltip(
                `<div>
                     HORTOLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4c7c04d9a400a0b802a851efff11ca3b = L.circleMarker(
                [-22.3681, -46.9431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_922a4b5a19a15dea955f370165a347b3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_49b51b51e853c9cda88556ce1b56ffab = $(`<div id="html_49b51b51e853c9cda88556ce1b56ffab" style="width: 100.0%; height: 100.0%;"><b>MOGI GUACU (SP)</b><br>1 registros</div>`)[0];
                popup_922a4b5a19a15dea955f370165a347b3.setContent(html_49b51b51e853c9cda88556ce1b56ffab);
            
        

        circle_marker_4c7c04d9a400a0b802a851efff11ca3b.bindPopup(popup_922a4b5a19a15dea955f370165a347b3)
        ;

        
    
    
            circle_marker_4c7c04d9a400a0b802a851efff11ca3b.bindTooltip(
                `<div>
                     MOGI GUACU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5915d76c0d81a98179f065fe4b13d1fb = L.circleMarker(
                [-21.6731, -49.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_9fb92d6c42b67a010b8c1302ec0b87e6 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2cd3626d388a079a85511a70a31bfb00 = $(`<div id="html_2cd3626d388a079a85511a70a31bfb00" style="width: 100.0%; height: 100.0%;"><b>LINS (SP)</b><br>1 registros</div>`)[0];
                popup_9fb92d6c42b67a010b8c1302ec0b87e6.setContent(html_2cd3626d388a079a85511a70a31bfb00);
            
        

        circle_marker_5915d76c0d81a98179f065fe4b13d1fb.bindPopup(popup_9fb92d6c42b67a010b8c1302ec0b87e6)
        ;

        
    
    
            circle_marker_5915d76c0d81a98179f065fe4b13d1fb.bindTooltip(
                `<div>
                     LINS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5010e242346386046c2254c6e63e94c3 = L.circleMarker(
                [-23.1681, -47.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_639f17d3b73de951377cda933768ffc9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e54ab181c167670ff85035631f47d658 = $(`<div id="html_e54ab181c167670ff85035631f47d658" style="width: 100.0%; height: 100.0%;"><b>CERQUILHO (SP)</b><br>1 registros</div>`)[0];
                popup_639f17d3b73de951377cda933768ffc9.setContent(html_e54ab181c167670ff85035631f47d658);
            
        

        circle_marker_5010e242346386046c2254c6e63e94c3.bindPopup(popup_639f17d3b73de951377cda933768ffc9)
        ;

        
    
    
            circle_marker_5010e242346386046c2254c6e63e94c3.bindTooltip(
                `<div>
                     CERQUILHO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f9d9e2bfc63ba601c4ccfb5b9fbe7d56 = L.circleMarker(
                [-22.9731, -46.9981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_eb16fece4e40c4fcb97a46ae068b83c8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2bd4b7482bffda08ece33f755b444819 = $(`<div id="html_2bd4b7482bffda08ece33f755b444819" style="width: 100.0%; height: 100.0%;"><b>VALINHOS (SP)</b><br>1 registros</div>`)[0];
                popup_eb16fece4e40c4fcb97a46ae068b83c8.setContent(html_2bd4b7482bffda08ece33f755b444819);
            
        

        circle_marker_f9d9e2bfc63ba601c4ccfb5b9fbe7d56.bindPopup(popup_eb16fece4e40c4fcb97a46ae068b83c8)
        ;

        
    
    
            circle_marker_f9d9e2bfc63ba601c4ccfb5b9fbe7d56.bindTooltip(
                `<div>
                     VALINHOS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_13e6850837f85a1b8ca764f9b3ca220b = L.circleMarker(
                [-21.2089, -50.4331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8519efa332fa156ec9e12009905450e9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ca1ac33c7e5987deff4e081d5f2dc002 = $(`<div id="html_ca1ac33c7e5987deff4e081d5f2dc002" style="width: 100.0%; height: 100.0%;"><b>ARACATUBA (SP)</b><br>2 registros</div>`)[0];
                popup_8519efa332fa156ec9e12009905450e9.setContent(html_ca1ac33c7e5987deff4e081d5f2dc002);
            
        

        circle_marker_13e6850837f85a1b8ca764f9b3ca220b.bindPopup(popup_8519efa332fa156ec9e12009905450e9)
        ;

        
    
    
            circle_marker_13e6850837f85a1b8ca764f9b3ca220b.bindTooltip(
                `<div>
                     ARACATUBA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_83598c92b2cbfa39af73c9a9ad4cf889 = L.circleMarker(
                [-21.7642, -41.3297],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_591abeeb2bd4fc5dcf04a0fc096c5917 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ecddd603684ab097f5841214a63fb859 = $(`<div id="html_ecddd603684ab097f5841214a63fb859" style="width: 100.0%; height: 100.0%;"><b>CAMPOS DOS GOYTACAZES (RJ)</b><br>1 registros</div>`)[0];
                popup_591abeeb2bd4fc5dcf04a0fc096c5917.setContent(html_ecddd603684ab097f5841214a63fb859);
            
        

        circle_marker_83598c92b2cbfa39af73c9a9ad4cf889.bindPopup(popup_591abeeb2bd4fc5dcf04a0fc096c5917)
        ;

        
    
    
            circle_marker_83598c92b2cbfa39af73c9a9ad4cf889.bindTooltip(
                `<div>
                     CAMPOS DOS GOYTACAZES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f87cbfbd7fc444308c56abf2db82a907 = L.circleMarker(
                [-21.6231, -49.0731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_237e7f2cf6556303c5fa1e1df8f1b6ee = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e65daf874a316d008780e8198207e4b1 = $(`<div id="html_e65daf874a316d008780e8198207e4b1" style="width: 100.0%; height: 100.0%;"><b>BORBOREMA (SP)</b><br>1 registros</div>`)[0];
                popup_237e7f2cf6556303c5fa1e1df8f1b6ee.setContent(html_e65daf874a316d008780e8198207e4b1);
            
        

        circle_marker_f87cbfbd7fc444308c56abf2db82a907.bindPopup(popup_237e7f2cf6556303c5fa1e1df8f1b6ee)
        ;

        
    
    
            circle_marker_f87cbfbd7fc444308c56abf2db82a907.bindTooltip(
                `<div>
                     BORBOREMA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1427d6a26ca7f0e8015cb7d8cf747efa = L.circleMarker(
                [-23.6861, -46.6228],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_7fda66a6356a8e01234322fd2602f5f3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_101737a3c943bbaac3d6e046e062bfe5 = $(`<div id="html_101737a3c943bbaac3d6e046e062bfe5" style="width: 100.0%; height: 100.0%;"><b>DIADEMA (SP)</b><br>2 registros</div>`)[0];
                popup_7fda66a6356a8e01234322fd2602f5f3.setContent(html_101737a3c943bbaac3d6e046e062bfe5);
            
        

        circle_marker_1427d6a26ca7f0e8015cb7d8cf747efa.bindPopup(popup_7fda66a6356a8e01234322fd2602f5f3)
        ;

        
    
    
            circle_marker_1427d6a26ca7f0e8015cb7d8cf747efa.bindTooltip(
                `<div>
                     DIADEMA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_82b93c886aebdfc231086a5064aff694 = L.circleMarker(
                [-25.3842, -51.4617],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "orange", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_b62b4becb90642cb1d528f180f68b0c8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_35d8248e86ad5160cabaab261837732e = $(`<div id="html_35d8248e86ad5160cabaab261837732e" style="width: 100.0%; height: 100.0%;"><b>GUARAPUAVA (PR)</b><br>4 registros</div>`)[0];
                popup_b62b4becb90642cb1d528f180f68b0c8.setContent(html_35d8248e86ad5160cabaab261837732e);
            
        

        circle_marker_82b93c886aebdfc231086a5064aff694.bindPopup(popup_b62b4becb90642cb1d528f180f68b0c8)
        ;

        
    
    
            circle_marker_82b93c886aebdfc231086a5064aff694.bindTooltip(
                `<div>
                     GUARAPUAVA: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2aed49b1368b8905082522dec3eb4477 = L.circleMarker(
                [-22.7611, -47.1544],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 15, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a89392cc0cd6208056a4eaf18b4ba306 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f4fc70e458c4f99161c3a552b36ba0a2 = $(`<div id="html_f4fc70e458c4f99161c3a552b36ba0a2" style="width: 100.0%; height: 100.0%;"><b>PAULINIA (SP)</b><br>5 registros</div>`)[0];
                popup_a89392cc0cd6208056a4eaf18b4ba306.setContent(html_f4fc70e458c4f99161c3a552b36ba0a2);
            
        

        circle_marker_2aed49b1368b8905082522dec3eb4477.bindPopup(popup_a89392cc0cd6208056a4eaf18b4ba306)
        ;

        
    
    
            circle_marker_2aed49b1368b8905082522dec3eb4477.bindTooltip(
                `<div>
                     PAULINIA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_90789f18aa568ba6d785a353513ada28 = L.circleMarker(
                [-21.9681, -46.7981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_1c0ca3914ca0b7549266565882be3119 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_dc498b297cc8f04ad2d703010758d154 = $(`<div id="html_dc498b297cc8f04ad2d703010758d154" style="width: 100.0%; height: 100.0%;"><b>SAO JOAO DA BOA VISTA (SP)</b><br>1 registros</div>`)[0];
                popup_1c0ca3914ca0b7549266565882be3119.setContent(html_dc498b297cc8f04ad2d703010758d154);
            
        

        circle_marker_90789f18aa568ba6d785a353513ada28.bindPopup(popup_1c0ca3914ca0b7549266565882be3119)
        ;

        
    
    
            circle_marker_90789f18aa568ba6d785a353513ada28.bindTooltip(
                `<div>
                     SAO JOAO DA BOA VISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4f6590e77c1ddf08ab3d4d898da1ad0d = L.circleMarker(
                [-7.9406, -34.8728],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_47cf4eea8fd75c93cb3de1944d54894c = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9df832271af75c372be463b66690258e = $(`<div id="html_9df832271af75c372be463b66690258e" style="width: 100.0%; height: 100.0%;"><b>PAULISTA (PE)</b><br>1 registros</div>`)[0];
                popup_47cf4eea8fd75c93cb3de1944d54894c.setContent(html_9df832271af75c372be463b66690258e);
            
        

        circle_marker_4f6590e77c1ddf08ab3d4d898da1ad0d.bindPopup(popup_47cf4eea8fd75c93cb3de1944d54894c)
        ;

        
    
    
            circle_marker_4f6590e77c1ddf08ab3d4d898da1ad0d.bindTooltip(
                `<div>
                     PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_fe5dc20adb2bf055a19e55f9c9dc59e1 = L.circleMarker(
                [-20.4697, -54.6201],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkblue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_23f79264b45a7d3324e63f7af7e5a2ff = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a148ad374d335ee11c55da3893c2704d = $(`<div id="html_a148ad374d335ee11c55da3893c2704d" style="width: 100.0%; height: 100.0%;"><b>CAMPO GRANDE (MS)</b><br>2 registros</div>`)[0];
                popup_23f79264b45a7d3324e63f7af7e5a2ff.setContent(html_a148ad374d335ee11c55da3893c2704d);
            
        

        circle_marker_fe5dc20adb2bf055a19e55f9c9dc59e1.bindPopup(popup_23f79264b45a7d3324e63f7af7e5a2ff)
        ;

        
    
    
            circle_marker_fe5dc20adb2bf055a19e55f9c9dc59e1.bindTooltip(
                `<div>
                     CAMPO GRANDE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_29e6ae5815c3820a5d909ad7912cd045 = L.circleMarker(
                [-8.112, -35.0145],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_29c05a3615be2c7f5847970392d0256a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_87f865846e8c694a93f4c5cf0fb7384b = $(`<div id="html_87f865846e8c694a93f4c5cf0fb7384b" style="width: 100.0%; height: 100.0%;"><b>JABOATAO DOS GUARARAPES (PE)</b><br>1 registros</div>`)[0];
                popup_29c05a3615be2c7f5847970392d0256a.setContent(html_87f865846e8c694a93f4c5cf0fb7384b);
            
        

        circle_marker_29e6ae5815c3820a5d909ad7912cd045.bindPopup(popup_29c05a3615be2c7f5847970392d0256a)
        ;

        
    
    
            circle_marker_29e6ae5815c3820a5d909ad7912cd045.bindTooltip(
                `<div>
                     JABOATAO DOS GUARARAPES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_93c3dfd34d0f95c223d1fd7bb71fbbcd = L.circleMarker(
                [-8.0476, -34.877],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 9, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_e83ffb3433dbae6e93c69d9d1e5a472f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fb0a150e41bf559f71cc9449de45e5c6 = $(`<div id="html_fb0a150e41bf559f71cc9449de45e5c6" style="width: 100.0%; height: 100.0%;"><b>RECIFE (PE)</b><br>3 registros</div>`)[0];
                popup_e83ffb3433dbae6e93c69d9d1e5a472f.setContent(html_fb0a150e41bf559f71cc9449de45e5c6);
            
        

        circle_marker_93c3dfd34d0f95c223d1fd7bb71fbbcd.bindPopup(popup_e83ffb3433dbae6e93c69d9d1e5a472f)
        ;

        
    
    
            circle_marker_93c3dfd34d0f95c223d1fd7bb71fbbcd.bindTooltip(
                `<div>
                     RECIFE: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_e5429b7d60740deeb947c13da112c7d6 = L.circleMarker(
                [-15.6014, -56.0979],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_b533c34d961eefe234c09ba26aeeb89d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a88201b7ee5f3ae3d39e1795a2ae5f52 = $(`<div id="html_a88201b7ee5f3ae3d39e1795a2ae5f52" style="width: 100.0%; height: 100.0%;"><b>CUIABA (MT)</b><br>2 registros</div>`)[0];
                popup_b533c34d961eefe234c09ba26aeeb89d.setContent(html_a88201b7ee5f3ae3d39e1795a2ae5f52);
            
        

        circle_marker_e5429b7d60740deeb947c13da112c7d6.bindPopup(popup_b533c34d961eefe234c09ba26aeeb89d)
        ;

        
    
    
            circle_marker_e5429b7d60740deeb947c13da112c7d6.bindTooltip(
                `<div>
                     CUIABA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_021a4065d3f236353d1954cf1b7040bb = L.circleMarker(
                [-19.3931, -54.5631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkblue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_1a74b084e8eb95338b74f8779070ca21 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d5a8130c198b7e7508e193d8460e732a = $(`<div id="html_d5a8130c198b7e7508e193d8460e732a" style="width: 100.0%; height: 100.0%;"><b>SAO GABRIEL DO OESTE (MS)</b><br>1 registros</div>`)[0];
                popup_1a74b084e8eb95338b74f8779070ca21.setContent(html_d5a8130c198b7e7508e193d8460e732a);
            
        

        circle_marker_021a4065d3f236353d1954cf1b7040bb.bindPopup(popup_1a74b084e8eb95338b74f8779070ca21)
        ;

        
    
    
            circle_marker_021a4065d3f236353d1954cf1b7040bb.bindTooltip(
                `<div>
                     SAO GABRIEL DO OESTE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_16a37b1a98e48eef69e043ea58c1c3bb = L.circleMarker(
                [-22.5264, -41.9456],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8cb97d488f55e333620cbe72014ebe20 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4f5d2ad9eacac51b007172bc08daff02 = $(`<div id="html_4f5d2ad9eacac51b007172bc08daff02" style="width: 100.0%; height: 100.0%;"><b>RIO DAS OSTRAS (RJ)</b><br>1 registros</div>`)[0];
                popup_8cb97d488f55e333620cbe72014ebe20.setContent(html_4f5d2ad9eacac51b007172bc08daff02);
            
        

        circle_marker_16a37b1a98e48eef69e043ea58c1c3bb.bindPopup(popup_8cb97d488f55e333620cbe72014ebe20)
        ;

        
    
    
            circle_marker_16a37b1a98e48eef69e043ea58c1c3bb.bindTooltip(
                `<div>
                     RIO DAS OSTRAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d2d3eae08a48d61f67a0b1a694c3d9b6 = L.circleMarker(
                [-7.1195, -34.845],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "lightblue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_fddd7009e744f5016955dcd7a7836cdd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_71a913a248e389822151f639df72fddf = $(`<div id="html_71a913a248e389822151f639df72fddf" style="width: 100.0%; height: 100.0%;"><b>JOAO PESSOA (PB)</b><br>1 registros</div>`)[0];
                popup_fddd7009e744f5016955dcd7a7836cdd.setContent(html_71a913a248e389822151f639df72fddf);
            
        

        circle_marker_d2d3eae08a48d61f67a0b1a694c3d9b6.bindPopup(popup_fddd7009e744f5016955dcd7a7836cdd)
        ;

        
    
    
            circle_marker_d2d3eae08a48d61f67a0b1a694c3d9b6.bindTooltip(
                `<div>
                     JOAO PESSOA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_85a7dde8bacd0f732b7f99de2260e433 = L.circleMarker(
                [-26.9194, -49.0661],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_cd8294dbcd2fdc93485d55c9f43eaa5b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4cf6438b555485ab5bf1eee46d9f8d52 = $(`<div id="html_4cf6438b555485ab5bf1eee46d9f8d52" style="width: 100.0%; height: 100.0%;"><b>BLUMENAU (SC)</b><br>1 registros</div>`)[0];
                popup_cd8294dbcd2fdc93485d55c9f43eaa5b.setContent(html_4cf6438b555485ab5bf1eee46d9f8d52);
            
        

        circle_marker_85a7dde8bacd0f732b7f99de2260e433.bindPopup(popup_cd8294dbcd2fdc93485d55c9f43eaa5b)
        ;

        
    
    
            circle_marker_85a7dde8bacd0f732b7f99de2260e433.bindTooltip(
                `<div>
                     BLUMENAU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_eb41c005d157eef4a41ace1542ea1a63 = L.circleMarker(
                [-27.0964, -52.6181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_e4a964cd3841e227a48b4126889bc800 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_75f4123945e92934f679248322feff0e = $(`<div id="html_75f4123945e92934f679248322feff0e" style="width: 100.0%; height: 100.0%;"><b>CHAPECO (SC)</b><br>1 registros</div>`)[0];
                popup_e4a964cd3841e227a48b4126889bc800.setContent(html_75f4123945e92934f679248322feff0e);
            
        

        circle_marker_eb41c005d157eef4a41ace1542ea1a63.bindPopup(popup_e4a964cd3841e227a48b4126889bc800)
        ;

        
    
    
            circle_marker_eb41c005d157eef4a41ace1542ea1a63.bindTooltip(
                `<div>
                     CHAPECO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_54ca5667748ec0af8d9af9064eedb90d = L.circleMarker(
                [-26.8981, -49.2331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_50357b8d937d0ba9c7013a9ba9cee9a6 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d66614f02a4539e6afeb34f03d057510 = $(`<div id="html_d66614f02a4539e6afeb34f03d057510" style="width: 100.0%; height: 100.0%;"><b>INDAIAL (SC)</b><br>1 registros</div>`)[0];
                popup_50357b8d937d0ba9c7013a9ba9cee9a6.setContent(html_d66614f02a4539e6afeb34f03d057510);
            
        

        circle_marker_54ca5667748ec0af8d9af9064eedb90d.bindPopup(popup_50357b8d937d0ba9c7013a9ba9cee9a6)
        ;

        
    
    
            circle_marker_54ca5667748ec0af8d9af9064eedb90d.bindTooltip(
                `<div>
                     INDAIAL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c61b7d0b0c588db0bcbb1f08e0beeb02 = L.circleMarker(
                [-26.2431, -48.6381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_0fa4e25fd9cf3f2392eb3d82707e4012 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_219da6f3b5c3b7bc8fdeab2a1bdaccd4 = $(`<div id="html_219da6f3b5c3b7bc8fdeab2a1bdaccd4" style="width: 100.0%; height: 100.0%;"><b>SAO FRANCISCO DO SUL (SC)</b><br>1 registros</div>`)[0];
                popup_0fa4e25fd9cf3f2392eb3d82707e4012.setContent(html_219da6f3b5c3b7bc8fdeab2a1bdaccd4);
            
        

        circle_marker_c61b7d0b0c588db0bcbb1f08e0beeb02.bindPopup(popup_0fa4e25fd9cf3f2392eb3d82707e4012)
        ;

        
    
    
            circle_marker_c61b7d0b0c588db0bcbb1f08e0beeb02.bindTooltip(
                `<div>
                     SAO FRANCISCO DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d9042c41e55831ad924e8e091532c57c = L.circleMarker(
                [-26.3731, -48.7231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_80ccd3cb4eb2409cc8aa8bd92b852b6f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_47726981e75e06217947a442a4d61b4e = $(`<div id="html_47726981e75e06217947a442a4d61b4e" style="width: 100.0%; height: 100.0%;"><b>ARAQUARI (SC)</b><br>1 registros</div>`)[0];
                popup_80ccd3cb4eb2409cc8aa8bd92b852b6f.setContent(html_47726981e75e06217947a442a4d61b4e);
            
        

        circle_marker_d9042c41e55831ad924e8e091532c57c.bindPopup(popup_80ccd3cb4eb2409cc8aa8bd92b852b6f)
        ;

        
    
    
            circle_marker_d9042c41e55831ad924e8e091532c57c.bindTooltip(
                `<div>
                     ARAQUARI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b69e4f843d3f5bccab37b1582ee89da1 = L.circleMarker(
                [-15.3081, -49.5981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_6a649555e62c11c4211574284f4474fd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ffbed3e8a2d5686516ea503c387a8005 = $(`<div id="html_ffbed3e8a2d5686516ea503c387a8005" style="width: 100.0%; height: 100.0%;"><b>CERES (GO)</b><br>1 registros</div>`)[0];
                popup_6a649555e62c11c4211574284f4474fd.setContent(html_ffbed3e8a2d5686516ea503c387a8005);
            
        

        circle_marker_b69e4f843d3f5bccab37b1582ee89da1.bindPopup(popup_6a649555e62c11c4211574284f4474fd)
        ;

        
    
    
            circle_marker_b69e4f843d3f5bccab37b1582ee89da1.bindTooltip(
                `<div>
                     CERES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4e47ea5c69883825688b424a864d99bf = L.circleMarker(
                [-8.0089, -34.8553],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_de8e0b26b5118f28957dffdc521e28ba = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f1223780a8d1682a0544f89994b634d4 = $(`<div id="html_f1223780a8d1682a0544f89994b634d4" style="width: 100.0%; height: 100.0%;"><b>OLINDA (PE)</b><br>1 registros</div>`)[0];
                popup_de8e0b26b5118f28957dffdc521e28ba.setContent(html_f1223780a8d1682a0544f89994b634d4);
            
        

        circle_marker_4e47ea5c69883825688b424a864d99bf.bindPopup(popup_de8e0b26b5118f28957dffdc521e28ba)
        ;

        
    
    
            circle_marker_4e47ea5c69883825688b424a864d99bf.bindTooltip(
                `<div>
                     OLINDA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_d3ec6235390fc6e9ae0b910436658166 = L.circleMarker(
                [-22.4331, -46.9581],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8f2b8867d5a2fcda2a0fcaf2cf623c98 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_8b713e302700ecf4df4314647f0020c5 = $(`<div id="html_8b713e302700ecf4df4314647f0020c5" style="width: 100.0%; height: 100.0%;"><b>MOGI MIRIM (SP)</b><br>1 registros</div>`)[0];
                popup_8f2b8867d5a2fcda2a0fcaf2cf623c98.setContent(html_8b713e302700ecf4df4314647f0020c5);
            
        

        circle_marker_d3ec6235390fc6e9ae0b910436658166.bindPopup(popup_8f2b8867d5a2fcda2a0fcaf2cf623c98)
        ;

        
    
    
            circle_marker_d3ec6235390fc6e9ae0b910436658166.bindTooltip(
                `<div>
                     MOGI MIRIM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b962316ea114a03cfa68782ef0d9a222 = L.circleMarker(
                [-22.5931, -46.5281],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8e566830098da7a904a5c05a43d205f3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0f7df672fc922f2e08843f9204762099 = $(`<div id="html_0f7df672fc922f2e08843f9204762099" style="width: 100.0%; height: 100.0%;"><b>SOCORRO (SP)</b><br>1 registros</div>`)[0];
                popup_8e566830098da7a904a5c05a43d205f3.setContent(html_0f7df672fc922f2e08843f9204762099);
            
        

        circle_marker_b962316ea114a03cfa68782ef0d9a222.bindPopup(popup_8e566830098da7a904a5c05a43d205f3)
        ;

        
    
    
            circle_marker_b962316ea114a03cfa68782ef0d9a222.bindTooltip(
                `<div>
                     SOCORRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_839e143770f54270ebc5ee6e4aaee799 = L.circleMarker(
                [-19.8157, -43.9542],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 12, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_9489aefad821433593df11b17d4a2fbd = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_62394803a90bdad8de1441948c6de100 = $(`<div id="html_62394803a90bdad8de1441948c6de100" style="width: 100.0%; height: 100.0%;"><b>BELO HORIZONTE (MG)</b><br>4 registros</div>`)[0];
                popup_9489aefad821433593df11b17d4a2fbd.setContent(html_62394803a90bdad8de1441948c6de100);
            
        

        circle_marker_839e143770f54270ebc5ee6e4aaee799.bindPopup(popup_9489aefad821433593df11b17d4a2fbd)
        ;

        
    
    
            circle_marker_839e143770f54270ebc5ee6e4aaee799.bindTooltip(
                `<div>
                     BELO HORIZONTE: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2389243650d6691a93ae8df59f87bc2f = L.circleMarker(
                [-22.3831, -46.5664],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_d7ececf530680fd33f1dc33539a7dbc4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b7e55d697b0e92158db580796ac90a00 = $(`<div id="html_b7e55d697b0e92158db580796ac90a00" style="width: 100.0%; height: 100.0%;"><b>MONTE SIAO (MG)</b><br>1 registros</div>`)[0];
                popup_d7ececf530680fd33f1dc33539a7dbc4.setContent(html_b7e55d697b0e92158db580796ac90a00);
            
        

        circle_marker_2389243650d6691a93ae8df59f87bc2f.bindPopup(popup_d7ececf530680fd33f1dc33539a7dbc4)
        ;

        
    
    
            circle_marker_2389243650d6691a93ae8df59f87bc2f.bindTooltip(
                `<div>
                     MONTE SIAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_43cd98a01b291f0b75a3e8c83f584393 = L.circleMarker(
                [-16.6481, -49.4881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_1b446c6f4ecdffc40e3c3f1118d84ff0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f1e97cd154bdd6aeae85b550d985df35 = $(`<div id="html_f1e97cd154bdd6aeae85b550d985df35" style="width: 100.0%; height: 100.0%;"><b>TRINDADE (GO)</b><br>2 registros</div>`)[0];
                popup_1b446c6f4ecdffc40e3c3f1118d84ff0.setContent(html_f1e97cd154bdd6aeae85b550d985df35);
            
        

        circle_marker_43cd98a01b291f0b75a3e8c83f584393.bindPopup(popup_1b446c6f4ecdffc40e3c3f1118d84ff0)
        ;

        
    
    
            circle_marker_43cd98a01b291f0b75a3e8c83f584393.bindTooltip(
                `<div>
                     TRINDADE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_386137d63a9719e46c8989730677a85e = L.circleMarker(
                [-15.7801, -47.9292],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkred", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_0064ad9fbd1825f61ceb618e14184065 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c3b43caf48edf8216f313283b62beb2f = $(`<div id="html_c3b43caf48edf8216f313283b62beb2f" style="width: 100.0%; height: 100.0%;"><b>BRASILIA (DF)</b><br>1 registros</div>`)[0];
                popup_0064ad9fbd1825f61ceb618e14184065.setContent(html_c3b43caf48edf8216f313283b62beb2f);
            
        

        circle_marker_386137d63a9719e46c8989730677a85e.bindPopup(popup_0064ad9fbd1825f61ceb618e14184065)
        ;

        
    
    
            circle_marker_386137d63a9719e46c8989730677a85e.bindTooltip(
                `<div>
                     BRASILIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_134424b53294b6abb6ae752e187a0164 = L.circleMarker(
                [-23.6914, -46.5646],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_4bd2031e91485a4cf7c1c88e723995d0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a41547326ad115076d0fbcb55bf87958 = $(`<div id="html_a41547326ad115076d0fbcb55bf87958" style="width: 100.0%; height: 100.0%;"><b>SAO BERNARDO DO CAMPO (SP)</b><br>1 registros</div>`)[0];
                popup_4bd2031e91485a4cf7c1c88e723995d0.setContent(html_a41547326ad115076d0fbcb55bf87958);
            
        

        circle_marker_134424b53294b6abb6ae752e187a0164.bindPopup(popup_4bd2031e91485a4cf7c1c88e723995d0)
        ;

        
    
    
            circle_marker_134424b53294b6abb6ae752e187a0164.bindTooltip(
                `<div>
                     SAO BERNARDO DO CAMPO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2caa2b8672ca1fb82bf8d5e1c10293b6 = L.circleMarker(
                [-16.3281, -48.9531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_1ba80618226e42dc64bd4baf39585082 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0c28e677f823c634ac12cbb99da8d39d = $(`<div id="html_0c28e677f823c634ac12cbb99da8d39d" style="width: 100.0%; height: 100.0%;"><b>ANAPOLIS (GO)</b><br>1 registros</div>`)[0];
                popup_1ba80618226e42dc64bd4baf39585082.setContent(html_0c28e677f823c634ac12cbb99da8d39d);
            
        

        circle_marker_2caa2b8672ca1fb82bf8d5e1c10293b6.bindPopup(popup_1ba80618226e42dc64bd4baf39585082)
        ;

        
    
    
            circle_marker_2caa2b8672ca1fb82bf8d5e1c10293b6.bindTooltip(
                `<div>
                     ANAPOLIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2c20076c56d5171bd72e9f27c3d9b3c8 = L.circleMarker(
                [-16.4706, -54.6364],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_f0bdf01e82635a148c16b91ac9855992 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_dd579f56bac89846c16db35713654012 = $(`<div id="html_dd579f56bac89846c16db35713654012" style="width: 100.0%; height: 100.0%;"><b>RONDONOPOLIS (MT)</b><br>2 registros</div>`)[0];
                popup_f0bdf01e82635a148c16b91ac9855992.setContent(html_dd579f56bac89846c16db35713654012);
            
        

        circle_marker_2c20076c56d5171bd72e9f27c3d9b3c8.bindPopup(popup_f0bdf01e82635a148c16b91ac9855992)
        ;

        
    
    
            circle_marker_2c20076c56d5171bd72e9f27c3d9b3c8.bindTooltip(
                `<div>
                     RONDONOPOLIS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c588442180f4aee1e2d52d75daf59587 = L.circleMarker(
                [-23.6236, -46.5547],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_2b7e94fab782e6c10fec610590df6c23 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_bc723cc4bcbd5e467854e4ce47cc22d8 = $(`<div id="html_bc723cc4bcbd5e467854e4ce47cc22d8" style="width: 100.0%; height: 100.0%;"><b>SAO CAETANO DO SUL (SP)</b><br>1 registros</div>`)[0];
                popup_2b7e94fab782e6c10fec610590df6c23.setContent(html_bc723cc4bcbd5e467854e4ce47cc22d8);
            
        

        circle_marker_c588442180f4aee1e2d52d75daf59587.bindPopup(popup_2b7e94fab782e6c10fec610590df6c23)
        ;

        
    
    
            circle_marker_c588442180f4aee1e2d52d75daf59587.bindTooltip(
                `<div>
                     SAO CAETANO DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_67dcbec4ae2f2b3a1585615664e08720 = L.circleMarker(
                [-23.8131, -45.4031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_3e22c7c5f8a26acf0998fee20c261e92 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9749a10e86265565202353e5b8e85000 = $(`<div id="html_9749a10e86265565202353e5b8e85000" style="width: 100.0%; height: 100.0%;"><b>SAO SEBASTIAO (SP)</b><br>1 registros</div>`)[0];
                popup_3e22c7c5f8a26acf0998fee20c261e92.setContent(html_9749a10e86265565202353e5b8e85000);
            
        

        circle_marker_67dcbec4ae2f2b3a1585615664e08720.bindPopup(popup_3e22c7c5f8a26acf0998fee20c261e92)
        ;

        
    
    
            circle_marker_67dcbec4ae2f2b3a1585615664e08720.bindTooltip(
                `<div>
                     SAO SEBASTIAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7f12f91cacabfc0b29d7697851440fce = L.circleMarker(
                [-22.5981, -48.8031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_ced00a1685ad1c9fe93944c9bd2c2354 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_49896e9564b0f5307e498ccf1dd07f48 = $(`<div id="html_49896e9564b0f5307e498ccf1dd07f48" style="width: 100.0%; height: 100.0%;"><b>LENCOIS PAULISTA (SP)</b><br>1 registros</div>`)[0];
                popup_ced00a1685ad1c9fe93944c9bd2c2354.setContent(html_49896e9564b0f5307e498ccf1dd07f48);
            
        

        circle_marker_7f12f91cacabfc0b29d7697851440fce.bindPopup(popup_ced00a1685ad1c9fe93944c9bd2c2354)
        ;

        
    
    
            circle_marker_7f12f91cacabfc0b29d7697851440fce.bindTooltip(
                `<div>
                     LENCOIS PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3370aead1f449ae40c6d5db00541eaf8 = L.circleMarker(
                [-22.2831, -46.3664],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_67cd0ce33f6cad85dbb63a8b7e08d82f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7eeea9f914e2a787ab04ee1a8ee9c93e = $(`<div id="html_7eeea9f914e2a787ab04ee1a8ee9c93e" style="width: 100.0%; height: 100.0%;"><b>OURO FINO (MG)</b><br>1 registros</div>`)[0];
                popup_67cd0ce33f6cad85dbb63a8b7e08d82f.setContent(html_7eeea9f914e2a787ab04ee1a8ee9c93e);
            
        

        circle_marker_3370aead1f449ae40c6d5db00541eaf8.bindPopup(popup_67cd0ce33f6cad85dbb63a8b7e08d82f)
        ;

        
    
    
            circle_marker_3370aead1f449ae40c6d5db00541eaf8.bindTooltip(
                `<div>
                     OURO FINO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_53624ad81d03ed210e7ae10a7f5d0d4c = L.circleMarker(
                [-13.8331, -56.0831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_6bf495206b781d84913d9659f8a83242 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_3d14f2d24c8748d3a6a02123aab38dec = $(`<div id="html_3d14f2d24c8748d3a6a02123aab38dec" style="width: 100.0%; height: 100.0%;"><b>NOVA MUTUM (MT)</b><br>1 registros</div>`)[0];
                popup_6bf495206b781d84913d9659f8a83242.setContent(html_3d14f2d24c8748d3a6a02123aab38dec);
            
        

        circle_marker_53624ad81d03ed210e7ae10a7f5d0d4c.bindPopup(popup_6bf495206b781d84913d9659f8a83242)
        ;

        
    
    
            circle_marker_53624ad81d03ed210e7ae10a7f5d0d4c.bindTooltip(
                `<div>
                     NOVA MUTUM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4b35d2cd2bb1aed3ec29d667962a535d = L.circleMarker(
                [-23.0131, -48.0131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_78f72a2c62fbee3efdb6c039e9ce5d32 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4d153566245f36fb6d1a5d56aee3a2ca = $(`<div id="html_4d153566245f36fb6d1a5d56aee3a2ca" style="width: 100.0%; height: 100.0%;"><b>CONCHAS (SP)</b><br>1 registros</div>`)[0];
                popup_78f72a2c62fbee3efdb6c039e9ce5d32.setContent(html_4d153566245f36fb6d1a5d56aee3a2ca);
            
        

        circle_marker_4b35d2cd2bb1aed3ec29d667962a535d.bindPopup(popup_78f72a2c62fbee3efdb6c039e9ce5d32)
        ;

        
    
    
            circle_marker_4b35d2cd2bb1aed3ec29d667962a535d.bindTooltip(
                `<div>
                     CONCHAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_043caa33eba9cee1787154a1f46a160c = L.circleMarker(
                [-3.7319, -38.5267],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkgreen", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_cedcd6fc2d0b038be44b7c6c8a9468f6 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f750e9190e261ead0cae40d6bf8add72 = $(`<div id="html_f750e9190e261ead0cae40d6bf8add72" style="width: 100.0%; height: 100.0%;"><b>FORTALEZA (CE)</b><br>1 registros</div>`)[0];
                popup_cedcd6fc2d0b038be44b7c6c8a9468f6.setContent(html_f750e9190e261ead0cae40d6bf8add72);
            
        

        circle_marker_043caa33eba9cee1787154a1f46a160c.bindPopup(popup_cedcd6fc2d0b038be44b7c6c8a9468f6)
        ;

        
    
    
            circle_marker_043caa33eba9cee1787154a1f46a160c.bindTooltip(
                `<div>
                     FORTALEZA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_21ab68d217aab7e7d22f1d9c9dec3298 = L.circleMarker(
                [-16.7031, -49.0931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_fded09528095a85cc2fc55526d20b9e1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_395af18629fe97135ffd760a7ac1262c = $(`<div id="html_395af18629fe97135ffd760a7ac1262c" style="width: 100.0%; height: 100.0%;"><b>SENADOR CANEDO (GO)</b><br>1 registros</div>`)[0];
                popup_fded09528095a85cc2fc55526d20b9e1.setContent(html_395af18629fe97135ffd760a7ac1262c);
            
        

        circle_marker_21ab68d217aab7e7d22f1d9c9dec3298.bindPopup(popup_fded09528095a85cc2fc55526d20b9e1)
        ;

        
    
    
            circle_marker_21ab68d217aab7e7d22f1d9c9dec3298.bindTooltip(
                `<div>
                     SENADOR CANEDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3ea1b5b84917fa2ae7d759ab3f32b087 = L.circleMarker(
                [-22.4381, -46.8231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_6b0d243aad69cb4461edef777b7aa307 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_50a6ad1f5608e96c24e2c7b1956646a3 = $(`<div id="html_50a6ad1f5608e96c24e2c7b1956646a3" style="width: 100.0%; height: 100.0%;"><b>ITAPIRA (SP)</b><br>1 registros</div>`)[0];
                popup_6b0d243aad69cb4461edef777b7aa307.setContent(html_50a6ad1f5608e96c24e2c7b1956646a3);
            
        

        circle_marker_3ea1b5b84917fa2ae7d759ab3f32b087.bindPopup(popup_6b0d243aad69cb4461edef777b7aa307)
        ;

        
    
    
            circle_marker_3ea1b5b84917fa2ae7d759ab3f32b087.bindTooltip(
                `<div>
                     ITAPIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a5c19e1109eff73e67d7f900eebbda33 = L.circleMarker(
                [-20.4644, -45.4264],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_ad10ce7d1b9bcee70172ba30c1bd969a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_14aa5b905fa20a62603edcd63e63939e = $(`<div id="html_14aa5b905fa20a62603edcd63e63939e" style="width: 100.0%; height: 100.0%;"><b>FORMIGA (MG)</b><br>1 registros</div>`)[0];
                popup_ad10ce7d1b9bcee70172ba30c1bd969a.setContent(html_14aa5b905fa20a62603edcd63e63939e);
            
        

        circle_marker_a5c19e1109eff73e67d7f900eebbda33.bindPopup(popup_ad10ce7d1b9bcee70172ba30c1bd969a)
        ;

        
    
    
            circle_marker_a5c19e1109eff73e67d7f900eebbda33.bindTooltip(
                `<div>
                     FORMIGA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_10470bbf5c711b3c59460aefd9471730 = L.circleMarker(
                [-20.2331, -46.3731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_76eb9e2e18a86ae5ce5cdb52a440b3ee = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_06dfca37c34598bb84caf2aab1904fe1 = $(`<div id="html_06dfca37c34598bb84caf2aab1904fe1" style="width: 100.0%; height: 100.0%;"><b>SAO ROQUE DE MINAS (MG)</b><br>1 registros</div>`)[0];
                popup_76eb9e2e18a86ae5ce5cdb52a440b3ee.setContent(html_06dfca37c34598bb84caf2aab1904fe1);
            
        

        circle_marker_10470bbf5c711b3c59460aefd9471730.bindPopup(popup_76eb9e2e18a86ae5ce5cdb52a440b3ee)
        ;

        
    
    
            circle_marker_10470bbf5c711b3c59460aefd9471730.bindTooltip(
                `<div>
                     SAO ROQUE DE MINAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_36094e190a612341bf5ddd957a481a91 = L.circleMarker(
                [-23.0281, -46.9731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_beaae79b17d7a6537a72017621bdb843 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7eb24bb99a48ccdce5470388ba84e5df = $(`<div id="html_7eb24bb99a48ccdce5470388ba84e5df" style="width: 100.0%; height: 100.0%;"><b>VINHEDO (SP)</b><br>1 registros</div>`)[0];
                popup_beaae79b17d7a6537a72017621bdb843.setContent(html_7eb24bb99a48ccdce5470388ba84e5df);
            
        

        circle_marker_36094e190a612341bf5ddd957a481a91.bindPopup(popup_beaae79b17d7a6537a72017621bdb843)
        ;

        
    
    
            circle_marker_36094e190a612341bf5ddd957a481a91.bindTooltip(
                `<div>
                     VINHEDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_99779e041851c4b245a78c2fcba47fb1 = L.circleMarker(
                [-22.9068, -43.1729],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_9bb14b67f6cebfca8b0e3e6555bb9a59 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9cc95b215b621a0856b575554d35933e = $(`<div id="html_9cc95b215b621a0856b575554d35933e" style="width: 100.0%; height: 100.0%;"><b>RIO DE JANEIRO (RJ)</b><br>1 registros</div>`)[0];
                popup_9bb14b67f6cebfca8b0e3e6555bb9a59.setContent(html_9cc95b215b621a0856b575554d35933e);
            
        

        circle_marker_99779e041851c4b245a78c2fcba47fb1.bindPopup(popup_9bb14b67f6cebfca8b0e3e6555bb9a59)
        ;

        
    
    
            circle_marker_99779e041851c4b245a78c2fcba47fb1.bindTooltip(
                `<div>
                     RIO DE JANEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ba3de6bc78c0ff7e64220db7689b1cd6 = L.circleMarker(
                [-23.2031, -47.2831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_cc3c5e7a58d450eec26acde3353ed6b2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_012452c94aa8220235d9d90ff9852269 = $(`<div id="html_012452c94aa8220235d9d90ff9852269" style="width: 100.0%; height: 100.0%;"><b>SALTO (SP)</b><br>1 registros</div>`)[0];
                popup_cc3c5e7a58d450eec26acde3353ed6b2.setContent(html_012452c94aa8220235d9d90ff9852269);
            
        

        circle_marker_ba3de6bc78c0ff7e64220db7689b1cd6.bindPopup(popup_cc3c5e7a58d450eec26acde3353ed6b2)
        ;

        
    
    
            circle_marker_ba3de6bc78c0ff7e64220db7689b1cd6.bindTooltip(
                `<div>
                     SALTO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_e8f9eb162b089d451ab4f69eca60d6ba = L.circleMarker(
                [-11.5031, -54.8831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_56bb68972aa5586ca67dbb65a71ec71e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e5e3124eabf69373517228cb50ff0a49 = $(`<div id="html_e5e3124eabf69373517228cb50ff0a49" style="width: 100.0%; height: 100.0%;"><b>CLAUDIA (MT)</b><br>1 registros</div>`)[0];
                popup_56bb68972aa5586ca67dbb65a71ec71e.setContent(html_e5e3124eabf69373517228cb50ff0a49);
            
        

        circle_marker_e8f9eb162b089d451ab4f69eca60d6ba.bindPopup(popup_56bb68972aa5586ca67dbb65a71ec71e)
        ;

        
    
    
            circle_marker_e8f9eb162b089d451ab4f69eca60d6ba.bindTooltip(
                `<div>
                     CLAUDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9bcc72dade32a6bfe7ccf34003db6f96 = L.circleMarker(
                [-16.6869, -49.2648],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_c397e6b3dd7e22b081185eb101a86204 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cc16f54ab5d4aa5870a764ed42556314 = $(`<div id="html_cc16f54ab5d4aa5870a764ed42556314" style="width: 100.0%; height: 100.0%;"><b>GOIANIA (GO)</b><br>2 registros</div>`)[0];
                popup_c397e6b3dd7e22b081185eb101a86204.setContent(html_cc16f54ab5d4aa5870a764ed42556314);
            
        

        circle_marker_9bcc72dade32a6bfe7ccf34003db6f96.bindPopup(popup_c397e6b3dd7e22b081185eb101a86204)
        ;

        
    
    
            circle_marker_9bcc72dade32a6bfe7ccf34003db6f96.bindTooltip(
                `<div>
                     GOIANIA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6df6aa9969eb540d40f807c465110ad0 = L.circleMarker(
                [-16.7353, -43.8619],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_de52176e122fd1e41609e6669ccfb824 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ca17e3ef29e4c532f42c807427a03573 = $(`<div id="html_ca17e3ef29e4c532f42c807427a03573" style="width: 100.0%; height: 100.0%;"><b>MONTES CLAROS (MG)</b><br>1 registros</div>`)[0];
                popup_de52176e122fd1e41609e6669ccfb824.setContent(html_ca17e3ef29e4c532f42c807427a03573);
            
        

        circle_marker_6df6aa9969eb540d40f807c465110ad0.bindPopup(popup_de52176e122fd1e41609e6669ccfb824)
        ;

        
    
    
            circle_marker_6df6aa9969eb540d40f807c465110ad0.bindTooltip(
                `<div>
                     MONTES CLAROS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1395f84a4d0408015b5efcb63c43dd5b = L.circleMarker(
                [-10.9472, -37.0731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "lightgray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_b774e7ca43bc8d97ae1059349b223f69 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_876973ae3d52494d2689fa3dc3b34036 = $(`<div id="html_876973ae3d52494d2689fa3dc3b34036" style="width: 100.0%; height: 100.0%;"><b>ARACAJU (SE)</b><br>1 registros</div>`)[0];
                popup_b774e7ca43bc8d97ae1059349b223f69.setContent(html_876973ae3d52494d2689fa3dc3b34036);
            
        

        circle_marker_1395f84a4d0408015b5efcb63c43dd5b.bindPopup(popup_b774e7ca43bc8d97ae1059349b223f69)
        ;

        
    
    
            circle_marker_1395f84a4d0408015b5efcb63c43dd5b.bindTooltip(
                `<div>
                     ARACAJU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4726c7ce8230182dc7420b7fffae254d = L.circleMarker(
                [-18.1681, -47.9481],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "brown", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_d7537d562be59eec27406033c006e853 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cd49d033df5563c5cc8825d893f32a84 = $(`<div id="html_cd49d033df5563c5cc8825d893f32a84" style="width: 100.0%; height: 100.0%;"><b>CATALAO (GO)</b><br>1 registros</div>`)[0];
                popup_d7537d562be59eec27406033c006e853.setContent(html_cd49d033df5563c5cc8825d893f32a84);
            
        

        circle_marker_4726c7ce8230182dc7420b7fffae254d.bindPopup(popup_d7537d562be59eec27406033c006e853)
        ;

        
    
    
            circle_marker_4726c7ce8230182dc7420b7fffae254d.bindTooltip(
                `<div>
                     CATALAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7ca1aa5d1f23b40269fe6ac89e1e8094 = L.circleMarker(
                [-14.6231, -57.5031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "gray", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_6b73853479c59ea6a1bbc8801ca7b687 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_dd951c131863552c8550f5a27195f415 = $(`<div id="html_dd951c131863552c8550f5a27195f415" style="width: 100.0%; height: 100.0%;"><b>TANGARA DA SERRA (MT)</b><br>1 registros</div>`)[0];
                popup_6b73853479c59ea6a1bbc8801ca7b687.setContent(html_dd951c131863552c8550f5a27195f415);
            
        

        circle_marker_7ca1aa5d1f23b40269fe6ac89e1e8094.bindPopup(popup_6b73853479c59ea6a1bbc8801ca7b687)
        ;

        
    
    
            circle_marker_7ca1aa5d1f23b40269fe6ac89e1e8094.bindTooltip(
                `<div>
                     TANGARA DA SERRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1cff4f1c735275665c52bf3e84281a26 = L.circleMarker(
                [-7.5764, -40.4975],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "pink", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_b31452d7895c89e4b70bea2f7b820999 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_bd4a2f3979f1b7b1d8ee4892c87c7695 = $(`<div id="html_bd4a2f3979f1b7b1d8ee4892c87c7695" style="width: 100.0%; height: 100.0%;"><b>ARARIPINA (PE)</b><br>1 registros</div>`)[0];
                popup_b31452d7895c89e4b70bea2f7b820999.setContent(html_bd4a2f3979f1b7b1d8ee4892c87c7695);
            
        

        circle_marker_1cff4f1c735275665c52bf3e84281a26.bindPopup(popup_b31452d7895c89e4b70bea2f7b820999)
        ;

        
    
    
            circle_marker_1cff4f1c735275665c52bf3e84281a26.bindTooltip(
                `<div>
                     ARARIPINA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ab135cc68d9744f764c5039f0f24f6b1 = L.circleMarker(
                [-9.4111, -40.4986],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "lightred", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a87c0dc636729eb72afccc4715769c50 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_009fef0a638b80425663f3bda3abfc94 = $(`<div id="html_009fef0a638b80425663f3bda3abfc94" style="width: 100.0%; height: 100.0%;"><b>JUAZEIRO (BA)</b><br>1 registros</div>`)[0];
                popup_a87c0dc636729eb72afccc4715769c50.setContent(html_009fef0a638b80425663f3bda3abfc94);
            
        

        circle_marker_ab135cc68d9744f764c5039f0f24f6b1.bindPopup(popup_a87c0dc636729eb72afccc4715769c50)
        ;

        
    
    
            circle_marker_ab135cc68d9744f764c5039f0f24f6b1.bindTooltip(
                `<div>
                     JUAZEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0e2acd16c4214f435feb8210babcafe5 = L.circleMarker(
                [-19.9317, -44.0536],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_140f96f724af977aedc3d1b03027cdfb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fb90d414e9d9f2158a5484406f95253e = $(`<div id="html_fb90d414e9d9f2158a5484406f95253e" style="width: 100.0%; height: 100.0%;"><b>CONTAGEM (MG)</b><br>1 registros</div>`)[0];
                popup_140f96f724af977aedc3d1b03027cdfb.setContent(html_fb90d414e9d9f2158a5484406f95253e);
            
        

        circle_marker_0e2acd16c4214f435feb8210babcafe5.bindPopup(popup_140f96f724af977aedc3d1b03027cdfb)
        ;

        
    
    
            circle_marker_0e2acd16c4214f435feb8210babcafe5.bindTooltip(
                `<div>
                     CONTAGEM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6537dc574b9157812658a6ede1dd42ae = L.circleMarker(
                [-3.5431, -40.6431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "darkgreen", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_4a895a061118bdc13561071f1b94e211 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_774a0437ede1e0a5858a0317c4a89627 = $(`<div id="html_774a0437ede1e0a5858a0317c4a89627" style="width: 100.0%; height: 100.0%;"><b>COREAU (CE)</b><br>1 registros</div>`)[0];
                popup_4a895a061118bdc13561071f1b94e211.setContent(html_774a0437ede1e0a5858a0317c4a89627);
            
        

        circle_marker_6537dc574b9157812658a6ede1dd42ae.bindPopup(popup_4a895a061118bdc13561071f1b94e211)
        ;

        
    
    
            circle_marker_6537dc574b9157812658a6ede1dd42ae.bindTooltip(
                `<div>
                     COREAU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_bf123dbfedb51f7f45284c5363213d29 = L.circleMarker(
                [-22.6631, -50.4131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_8e4e65f3621ddcc2ce604b3a185c4118 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f755e18a22d91009054c13c929306c21 = $(`<div id="html_f755e18a22d91009054c13c929306c21" style="width: 100.0%; height: 100.0%;"><b>ASSIS (SP)</b><br>1 registros</div>`)[0];
                popup_8e4e65f3621ddcc2ce604b3a185c4118.setContent(html_f755e18a22d91009054c13c929306c21);
            
        

        circle_marker_bf123dbfedb51f7f45284c5363213d29.bindPopup(popup_8e4e65f3621ddcc2ce604b3a185c4118)
        ;

        
    
    
            circle_marker_bf123dbfedb51f7f45284c5363213d29.bindTooltip(
                `<div>
                     ASSIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_eedcc176cc4c46f23b3d8f19864d8c70 = L.circleMarker(
                [-26.8231, -49.2731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "purple", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_edd58de8f0bba98333e51ce17b247028);
        
    
        var popup_a85372fdf6255dddee7910b62c11d98f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a3cd19431d949b255ba5b29a9b71d12b = $(`<div id="html_a3cd19431d949b255ba5b29a9b71d12b" style="width: 100.0%; height: 100.0%;"><b>TIMBO (SC)</b><br>1 registros</div>`)[0];
                popup_a85372fdf6255dddee7910b62c11d98f.setContent(html_a3cd19431d949b255ba5b29a9b71d12b);
            
        

        circle_marker_eedcc176cc4c46f23b3d8f19864d8c70.bindPopup(popup_a85372fdf6255dddee7910b62c11d98f)
        ;

        
    
    
            circle_marker_eedcc176cc4c46f23b3d8f19864d8c70.bindTooltip(
                `<div>
                     TIMBO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
</script>
</html>