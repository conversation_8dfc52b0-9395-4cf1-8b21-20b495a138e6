#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar visualização detalhada por municípios no mapa do Brasil
com técnicas para lidar com a escala e densidade de dados
"""

import pandas as pd
import matplotlib.pyplot as plt
import csv
from collections import Counter
import numpy as np

def analisar_csv_municipios(arquivo_entrada):
    """
    Analisa o arquivo CSV e prepara dados detalhados por município
    """
    dados_municipios = []

    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho

            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')

                    if cidade and estado:
                        dados_municipios.append((cidade, estado))

    except UnicodeDecodeError:
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)

            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')

                    if cidade and estado:
                        dados_municipios.append((cidade, estado))

    # Conta ocorrências por município
    contador_municipios = Counter(dados_municipios)
    contador_cidades = Counter([cidade for cidade, estado in dados_municipios])
    contador_estados = Counter([estado for cidade, estado in dados_municipios])

    return contador_municipios, contador_cidades, contador_estados

def obter_coordenadas_municipios():
    """
    Base de coordenadas aproximadas para os principais municípios
    Baseado nas capitais e principais cidades do Brasil
    """
    coordenadas = {
        # São Paulo
        ('SAO PAULO', 'SP'): {'lat': -23.5505, 'lon': -46.6333},
        ('CAMPINAS', 'SP'): {'lat': -22.9056, 'lon': -47.0608},
        ('GUARULHOS', 'SP'): {'lat': -23.4538, 'lon': -46.5333},
        ('SAO JOSE DOS CAMPOS', 'SP'): {'lat': -23.1794, 'lon': -45.8869},
        ('SOROCABA', 'SP'): {'lat': -23.5015, 'lon': -47.4526},
        ('SANTOS', 'SP'): {'lat': -23.9608, 'lon': -46.3331},
        ('RIBEIRAO PRETO', 'SP'): {'lat': -21.1775, 'lon': -47.8103},
        ('BAURU', 'SP'): {'lat': -22.3208, 'lon': -49.0608},
        ('SAO BERNARDO DO CAMPO', 'SP'): {'lat': -23.6914, 'lon': -46.5646},
        ('OSASCO', 'SP'): {'lat': -23.5329, 'lon': -46.7918},
        ('SANTO ANDRE', 'SP'): {'lat': -23.6629, 'lon': -46.5383},
        ('JUNDIAI', 'SP'): {'lat': -23.1864, 'lon': -46.8842},
        ('PIRACICABA', 'SP'): {'lat': -22.7253, 'lon': -47.6492},
        ('FRANCA', 'SP'): {'lat': -20.5386, 'lon': -47.4006},
        ('INDAIATUBA', 'SP'): {'lat': -23.0922, 'lon': -47.2181},
        ('SAO JOSE DO RIO PRETO', 'SP'): {'lat': -20.8197, 'lon': -49.3794},
        ('ARARAQUARA', 'SP'): {'lat': -21.7947, 'lon': -48.1756},
        ('AMERICANA', 'SP'): {'lat': -22.7394, 'lon': -47.3314},
        ('LIMEIRA', 'SP'): {'lat': -22.5647, 'lon': -47.4017},
        ('SUMARE', 'SP'): {'lat': -22.8219, 'lon': -47.2669},
        ('PAULINIA', 'SP'): {'lat': -22.7611, 'lon': -47.1544},
        ('SAO CAETANO DO SUL', 'SP'): {'lat': -23.6236, 'lon': -46.5547},
        ('MOGI DAS CRUZES', 'SP'): {'lat': -23.5225, 'lon': -46.1883},
        ('DIADEMA', 'SP'): {'lat': -23.6861, 'lon': -46.6228},
        ('RIO CLARO', 'SP'): {'lat': -22.4114, 'lon': -47.5614},
        ('ITAPEVI', 'SP'): {'lat': -23.5489, 'lon': -46.9342},
        ('SUZANO', 'SP'): {'lat': -23.5425, 'lon': -46.3108},
        ('TABOAO DA SERRA', 'SP'): {'lat': -23.6092, 'lon': -46.7581},
        ('BARUERI', 'SP'): {'lat': -23.5106, 'lon': -46.8761},
        ('EMBU DAS ARTES', 'SP'): {'lat': -23.6489, 'lon': -46.8522},

        # Rio de Janeiro
        ('RIO DE JANEIRO', 'RJ'): {'lat': -22.9068, 'lon': -43.1729},
        ('SAO GONCALO', 'RJ'): {'lat': -22.8267, 'lon': -43.0531},
        ('DUQUE DE CAXIAS', 'RJ'): {'lat': -22.7856, 'lon': -43.3117},
        ('NOVA IGUACU', 'RJ'): {'lat': -22.7592, 'lon': -43.4511},
        ('NITEROI', 'RJ'): {'lat': -22.8833, 'lon': -43.1036},
        ('CAMPOS DOS GOYTACAZES', 'RJ'): {'lat': -21.7642, 'lon': -41.3297},
        ('BELFORD ROXO', 'RJ'): {'lat': -22.7642, 'lon': -43.3997},
        ('SAO JOAO DE MERITI', 'RJ'): {'lat': -22.8031, 'lon': -43.3728},
        ('PETROPOLIS', 'RJ'): {'lat': -22.5053, 'lon': -43.1781},
        ('VOLTA REDONDA', 'RJ'): {'lat': -22.5231, 'lon': -44.1039},
        ('MAGÉ', 'RJ'): {'lat': -22.6558, 'lon': -43.0403},
        ('MACAE', 'RJ'): {'lat': -22.3711, 'lon': -41.7869},
        ('ITABORAI', 'RJ'): {'lat': -22.7444, 'lon': -42.8597},
        ('CABO FRIO', 'RJ'): {'lat': -22.8794, 'lon': -42.0186},
        ('ANGRA DOS REIS', 'RJ'): {'lat': -23.0067, 'lon': -44.3181},
        ('NOVA FRIBURGO', 'RJ'): {'lat': -22.2819, 'lon': -42.5311},
        ('BARRA MANSA', 'RJ'): {'lat': -22.5444, 'lon': -44.1731},
        ('TERESOPOLIS', 'RJ'): {'lat': -22.4125, 'lon': -42.9661},
        ('MESQUITA', 'RJ'): {'lat': -22.7831, 'lon': -43.4311},
        ('NILOPOLIS', 'RJ'): {'lat': -22.8081, 'lon': -43.4144},
        ('RIO DAS OSTRAS', 'RJ'): {'lat': -22.5264, 'lon': -41.9456},

        # Minas Gerais
        ('BELO HORIZONTE', 'MG'): {'lat': -19.8157, 'lon': -43.9542},
        ('UBERLANDIA', 'MG'): {'lat': -18.9113, 'lon': -48.2622},
        ('CONTAGEM', 'MG'): {'lat': -19.9317, 'lon': -44.0536},
        ('JUIZ DE FORA', 'MG'): {'lat': -21.7642, 'lon': -43.3503},
        ('BETIM', 'MG'): {'lat': -19.9678, 'lon': -44.1983},
        ('MONTES CLAROS', 'MG'): {'lat': -16.7353, 'lon': -43.8619},
        ('RIBEIRAO DAS NEVES', 'MG'): {'lat': -19.7667, 'lon': -44.0869},
        ('UBERABA', 'MG'): {'lat': -19.7483, 'lon': -47.9319},
        ('GOVERNADOR VALADARES', 'MG'): {'lat': -18.8511, 'lon': -41.9494},
        ('IPATINGA', 'MG'): {'lat': -19.4683, 'lon': -42.5369},
        ('SANTA LUZIA', 'MG'): {'lat': -19.7697, 'lon': -43.8514},
        ('POCOS DE CALDAS', 'MG'): {'lat': -21.7881, 'lon': -46.5614},
        ('TEOFILO OTONI', 'MG'): {'lat': -17.8594, 'lon': -41.5053},
        ('BARBACENA', 'MG'): {'lat': -21.2258, 'lon': -43.7736},
        ('SABARA', 'MG'): {'lat': -19.8831, 'lon': -43.8014},
        ('VARGINHA', 'MG'): {'lat': -21.5519, 'lon': -45.4306},
        ('CONSELHEIRO LAFAIETE', 'MG'): {'lat': -20.6597, 'lon': -43.7864},
        ('VESPASIANO', 'MG'): {'lat': -19.6919, 'lon': -43.9231},
        ('ITABIRA', 'MG'): {'lat': -19.6197, 'lon': -43.2269},
        ('ARAGUARI', 'MG'): {'lat': -18.6472, 'lon': -48.1886},
        ('PASSOS', 'MG'): {'lat': -20.7186, 'lon': -46.6097},
        ('CORONEL FABRICIANO', 'MG'): {'lat': -19.5181, 'lon': -42.6281},
        ('MURIAE', 'MG'): {'lat': -21.1306, 'lon': -42.3664},
        ('ITUIUTABA', 'MG'): {'lat': -18.9697, 'lon': -49.4647},
        ('LAVRAS', 'MG'): {'lat': -21.2453, 'lon': -45.0000},
        ('PATOS DE MINAS', 'MG'): {'lat': -18.5789, 'lon': -46.5181},
        ('POUSO ALEGRE', 'MG'): {'lat': -22.2300, 'lon': -45.9364},
        ('SANTA RITA DO SAPUCAI', 'MG'): {'lat': -22.2500, 'lon': -45.7000},
        ('SAO JOAO DEL REI', 'MG'): {'lat': -21.1364, 'lon': -44.2631},
        ('DIVINOPOLIS', 'MG'): {'lat': -20.1386, 'lon': -44.8839},
        ('FORMIGA', 'MG'): {'lat': -20.4644, 'lon': -45.4264},
        ('PATROCINIO', 'MG'): {'lat': -18.9431, 'lon': -46.9931},
        ('PIUMHI', 'MG'): {'lat': -20.4711, 'lon': -45.9564},
        ('SAO ROQUE DE MINAS', 'MG'): {'lat': -20.2331, 'lon': -46.3731},
        ('MONTE SIAO', 'MG'): {'lat': -22.3831, 'lon': -46.5664},
        ('OURO FINO', 'MG'): {'lat': -22.2831, 'lon': -46.3664},

        # Santa Catarina
        ('JOINVILLE', 'SC'): {'lat': -26.3044, 'lon': -48.8456},
        ('FLORIANOPOLIS', 'SC'): {'lat': -27.5954, 'lon': -48.5480},
        ('BLUMENAU', 'SC'): {'lat': -26.9194, 'lon': -49.0661},
        ('SAO JOSE', 'SC'): {'lat': -27.5969, 'lon': -48.6331},
        ('CRICIUMA', 'SC'): {'lat': -28.6778, 'lon': -49.3697},
        ('CHAPECO', 'SC'): {'lat': -27.0964, 'lon': -52.6181},
        ('ITAJAI', 'SC'): {'lat': -26.9078, 'lon': -48.6631},
        ('JARAGUA DO SUL', 'SC'): {'lat': -26.4869, 'lon': -49.0669},
        ('LAGES', 'SC'): {'lat': -27.8156, 'lon': -50.3264},
        ('PALHOÇA', 'SC'): {'lat': -27.6386, 'lon': -48.6700},
        ('BALNEARIO CAMBORIU', 'SC'): {'lat': -26.9775, 'lon': -48.6342},
        ('BRUSQUE', 'SC'): {'lat': -27.0986, 'lon': -48.9156},
        ('TUBARAO', 'SC'): {'lat': -28.4669, 'lon': -49.0075},
        ('SAO BENTO DO SUL', 'SC'): {'lat': -26.2500, 'lon': -49.3781},
        ('CACADOR', 'SC'): {'lat': -26.7731, 'lon': -51.0181},
        ('CAMBORIU', 'SC'): {'lat': -27.0256, 'lon': -48.6531},
        ('NAVEGANTES', 'SC'): {'lat': -26.8975, 'lon': -48.6531},
        ('CONCORDIA', 'SC'): {'lat': -27.2342, 'lon': -52.0281},
        ('RIO DO SUL', 'SC'): {'lat': -27.2142, 'lon': -49.6431},
        ('ARARANGUÁ', 'SC'): {'lat': -28.9356, 'lon': -49.4881},
        ('GASPAR', 'SC'): {'lat': -26.9331, 'lon': -49.0531},
        ('BIGUACU', 'SC'): {'lat': -27.4931, 'lon': -48.6581},
        ('INDAIAL', 'SC'): {'lat': -26.8981, 'lon': -49.2331},
        ('ITAPEMA', 'SC'): {'lat': -27.0931, 'lon': -48.6131},
        ('MAFRA', 'SC'): {'lat': -26.1131, 'lon': -49.8031},
        ('CANOINHAS', 'SC'): {'lat': -26.1781, 'lon': -50.3931},
        ('SOMBRIO', 'SC'): {'lat': -29.1031, 'lon': -49.6331},
        ('LAGUNA', 'SC'): {'lat': -28.4831, 'lon': -48.7831},
        ('IMBITUBA', 'SC'): {'lat': -28.2331, 'lon': -48.6731},
        ('ARAQUARI', 'SC'): {'lat': -26.3731, 'lon': -48.7231},
        ('SAO FRANCISCO DO SUL', 'SC'): {'lat': -26.2431, 'lon': -48.6381},
        ('PORTO BELO', 'SC'): {'lat': -27.1581, 'lon': -48.5531},
        ('POMERODE', 'SC'): {'lat': -26.7431, 'lon': -49.1781},
        ('TIMBO', 'SC'): {'lat': -26.8231, 'lon': -49.2731}
    }

    return coordenadas

def estimar_coordenadas_faltantes(contador_municipios, coordenadas_base):
    """
    Estima coordenadas para municípios não mapeados baseado no estado
    """
    coordenadas_estados = {
        'SP': {'lat': -23.5505, 'lon': -46.6333},
        'RJ': {'lat': -22.9068, 'lon': -43.1729},
        'MG': {'lat': -19.8157, 'lon': -43.9542},
        'SC': {'lat': -27.2423, 'lon': -50.2189},
        'PR': {'lat': -24.8936, 'lon': -51.4416},
        'GO': {'lat': -16.6869, 'lon': -49.2648},
        'PE': {'lat': -8.8137, 'lon': -36.9541},
        'MT': {'lat': -12.6819, 'lon': -56.9211},
        'MS': {'lat': -20.7722, 'lon': -54.7852},
        'CE': {'lat': -5.4984, 'lon': -39.3206},
        'PB': {'lat': -7.2400, 'lon': -36.7820},
        'DF': {'lat': -15.7801, 'lon': -47.9292},
        'SE': {'lat': -10.5741, 'lon': -37.3857},
        'BA': {'lat': -12.5797, 'lon': -41.7007}
    }

    coordenadas_completas = coordenadas_base.copy()

    # Para municípios sem coordenadas, usa a coordenada do estado com pequena variação
    for (cidade, estado), quantidade in contador_municipios.items():
        if (cidade, estado) not in coordenadas_completas and estado in coordenadas_estados:
            # Adiciona pequena variação aleatória para evitar sobreposição
            base_lat = coordenadas_estados[estado]['lat']
            base_lon = coordenadas_estados[estado]['lon']

            # Variação baseada no hash da cidade para ser consistente
            import hashlib
            hash_cidade = int(hashlib.md5(cidade.encode()).hexdigest()[:8], 16)

            variacao_lat = ((hash_cidade % 200) - 100) / 1000  # ±0.1 graus
            variacao_lon = (((hash_cidade // 200) % 200) - 100) / 1000

            coordenadas_completas[(cidade, estado)] = {
                'lat': base_lat + variacao_lat,
                'lon': base_lon + variacao_lon
            }

    return coordenadas_completas

def criar_mapa_municipios_detalhado(contador_municipios, contador_cidades, contador_estados):
    """
    Cria visualizações detalhadas por município
    """
    # Configuração do matplotlib
    plt.rcParams['figure.dpi'] = 300
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['font.size'] = 10

    # Obtém coordenadas
    coordenadas_base = obter_coordenadas_municipios()
    coordenadas_completas = estimar_coordenadas_faltantes(contador_municipios, coordenadas_base)

    # 1. Mapa geral do Brasil com todos os municípios
    fig1, ax1 = plt.subplots(figsize=(16, 12))

    # Cores por estado
    cores_estados = {
        'SP': '#1f77b4', 'RJ': '#ff7f0e', 'MG': '#2ca02c', 'SC': '#d62728',
        'PR': '#9467bd', 'GO': '#8c564b', 'PE': '#e377c2', 'MT': '#7f7f7f',
        'MS': '#bcbd22', 'CE': '#17becf', 'PB': '#ff9896', 'DF': '#c5b0d5',
        'SE': '#c49c94', 'BA': '#f7b6d3'
    }

    lats, lons, tamanhos, cores, nomes = [], [], [], [], []

    for (cidade, estado), quantidade in contador_municipios.items():
        if (cidade, estado) in coordenadas_completas:
            coord = coordenadas_completas[(cidade, estado)]
            lats.append(coord['lat'])
            lons.append(coord['lon'])
            tamanhos.append(max(quantidade * 20, 30))  # Tamanho mínimo 30
            cores.append(cores_estados.get(estado, '#gray'))
            nomes.append(f"{cidade} ({estado}): {quantidade}")

    # Cria o scatter plot
    scatter = ax1.scatter(lons, lats, s=tamanhos, c=cores, alpha=0.7, edgecolors='black', linewidth=0.5)

    # Configurações do mapa
    ax1.set_xlabel('Longitude', fontsize=12)
    ax1.set_ylabel('Latitude', fontsize=12)
    ax1.set_title('Mapa Detalhado - Distribuição por Municípios', fontsize=16, fontweight='bold')
    ax1.grid(True, alpha=0.3)

    # Adiciona anotações para os municípios com mais registros (top 10)
    top_municipios = contador_municipios.most_common(10)
    for (cidade, estado), quantidade in top_municipios:
        if (cidade, estado) in coordenadas_completas:
            coord = coordenadas_completas[(cidade, estado)]
            ax1.annotate(f'{cidade}\n({quantidade})',
                        (coord['lon'], coord['lat']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, fontweight='bold',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    # Legenda dos estados
    handles = [plt.scatter([], [], c=cor, s=100, label=estado)
              for estado, cor in cores_estados.items() if estado in contador_estados]
    ax1.legend(handles=handles, title='Estados', loc='upper left', bbox_to_anchor=(1, 1))

    plt.tight_layout()
    plt.savefig('mapa_municipios_brasil_completo.png', bbox_inches='tight', facecolor='white')
    plt.close()

    return coordenadas_completas

def criar_mapas_regionais(contador_municipios, coordenadas_completas):
    """
    Cria mapas regionais para melhor visualização
    """
    # Define regiões e seus estados
    regioes = {
        'Sudeste': ['SP', 'RJ', 'MG', 'ES'],
        'Sul': ['SC', 'PR', 'RS'],
        'Centro-Oeste': ['GO', 'MT', 'MS', 'DF'],
        'Nordeste': ['PE', 'CE', 'PB', 'SE', 'BA', 'AL', 'RN', 'PI', 'MA'],
        'Norte': ['AM', 'PA', 'AC', 'RO', 'RR', 'AP', 'TO']
    }

    cores_estados = {
        'SP': '#1f77b4', 'RJ': '#ff7f0e', 'MG': '#2ca02c', 'SC': '#d62728',
        'PR': '#9467bd', 'GO': '#8c564b', 'PE': '#e377c2', 'MT': '#7f7f7f',
        'MS': '#bcbd22', 'CE': '#17becf', 'PB': '#ff9896', 'DF': '#c5b0d5',
        'SE': '#c49c94', 'BA': '#f7b6d3'
    }

    # Cria mapa para a região Sudeste (maior concentração)
    fig, ax = plt.subplots(figsize=(14, 10))

    estados_sudeste = ['SP', 'RJ', 'MG']
    lats, lons, tamanhos, cores, nomes = [], [], [], [], []

    for (cidade, estado), quantidade in contador_municipios.items():
        if estado in estados_sudeste and (cidade, estado) in coordenadas_completas:
            coord = coordenadas_completas[(cidade, estado)]
            lats.append(coord['lat'])
            lons.append(coord['lon'])
            tamanhos.append(max(quantidade * 30, 40))
            cores.append(cores_estados.get(estado, '#gray'))
            nomes.append(f"{cidade}: {quantidade}")

    scatter = ax.scatter(lons, lats, s=tamanhos, c=cores, alpha=0.7, edgecolors='black', linewidth=0.5)

    # Adiciona anotações para todos os municípios da região
    for (cidade, estado), quantidade in contador_municipios.items():
        if estado in estados_sudeste and (cidade, estado) in coordenadas_completas:
            coord = coordenadas_completas[(cidade, estado)]
            ax.annotate(f'{cidade}\n({quantidade})',
                       (coord['lon'], coord['lat']),
                       xytext=(3, 3), textcoords='offset points',
                       fontsize=7, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))

    ax.set_xlabel('Longitude', fontsize=12)
    ax.set_ylabel('Latitude', fontsize=12)
    ax.set_title('Região Sudeste - Distribuição Detalhada por Municípios', fontsize=16, fontweight='bold')
    ax.grid(True, alpha=0.3)

    # Legenda
    handles = [plt.scatter([], [], c=cores_estados[estado], s=100, label=estado)
              for estado in estados_sudeste]
    ax.legend(handles=handles, title='Estados', loc='upper right')

    plt.tight_layout()
    plt.savefig('mapa_municipios_sudeste.png', bbox_inches='tight', facecolor='white')
    plt.close()

def criar_visualizacoes_complementares(contador_municipios, contador_cidades, contador_estados):
    """
    Cria visualizações complementares para análise
    """
    # 1. Gráfico de densidade por estado
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Gráfico de barras dos top 20 municípios
    top_municipios = contador_municipios.most_common(20)
    cidades_top = [f"{cidade} ({estado})" for (cidade, estado), _ in top_municipios]
    quantidades_top = [quantidade for _, quantidade in top_municipios]

    cores_barras = []
    cores_estados = {
        'SP': '#1f77b4', 'RJ': '#ff7f0e', 'MG': '#2ca02c', 'SC': '#d62728',
        'PR': '#9467bd', 'GO': '#8c564b', 'PE': '#e377c2', 'MT': '#7f7f7f',
        'MS': '#bcbd22', 'CE': '#17becf', 'PB': '#ff9896', 'DF': '#c5b0d5',
        'SE': '#c49c94', 'BA': '#f7b6d3'
    }

    for (cidade, estado), _ in top_municipios:
        cores_barras.append(cores_estados.get(estado, '#gray'))

    bars = ax1.barh(range(len(cidades_top)), quantidades_top, color=cores_barras)
    ax1.set_yticks(range(len(cidades_top)))
    ax1.set_yticklabels(cidades_top, fontsize=9)
    ax1.set_xlabel('Número de Registros')
    ax1.set_title('Top 20 Municípios com Mais Registros', fontweight='bold')
    ax1.invert_yaxis()

    # Adiciona valores nas barras
    for i, (bar, quantidade) in enumerate(zip(bars, quantidades_top)):
        ax1.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                str(quantidade), ha='left', va='center', fontweight='bold', fontsize=8)

    # 2. Distribuição de municípios por estado
    municipios_por_estado = {}
    for (cidade, estado), quantidade in contador_municipios.items():
        if estado not in municipios_por_estado:
            municipios_por_estado[estado] = 0
        municipios_por_estado[estado] += 1

    estados_ord = sorted(municipios_por_estado.items(), key=lambda x: x[1], reverse=True)
    estados_nomes = [estado for estado, _ in estados_ord]
    num_municipios = [num for _, num in estados_ord]

    cores_estados_graf = [cores_estados.get(estado, '#gray') for estado in estados_nomes]

    bars2 = ax2.bar(estados_nomes, num_municipios, color=cores_estados_graf)
    ax2.set_xlabel('Estados')
    ax2.set_ylabel('Número de Municípios')
    ax2.set_title('Número de Municípios por Estado', fontweight='bold')
    ax2.tick_params(axis='x', rotation=45)

    # Adiciona valores nas barras
    for bar, num in zip(bars2, num_municipios):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                str(num), ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('analise_municipios_complementar.png', bbox_inches='tight', facecolor='white')
    plt.close()

def gerar_relatorio_municipios(contador_municipios, contador_cidades, contador_estados, coordenadas_completas):
    """
    Gera relatório detalhado da análise por municípios
    """
    with open('relatorio_municipios_detalhado.txt', 'w', encoding='utf-8') as f:
        f.write("=" * 70 + "\n")
        f.write("RELATÓRIO DETALHADO - ANÁLISE POR MUNICÍPIOS\n")
        f.write("=" * 70 + "\n\n")

        f.write("RESUMO GERAL:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total de registros: {sum(contador_municipios.values())}\n")
        f.write(f"Municípios únicos: {len(contador_municipios)}\n")
        f.write(f"Estados representados: {len(contador_estados)}\n")
        f.write(f"Municípios com coordenadas mapeadas: {len([k for k in contador_municipios.keys() if k in coordenadas_completas])}\n\n")

        # Análise por estado
        f.write("ANÁLISE POR ESTADO:\n")
        f.write("-" * 30 + "\n")

        municipios_por_estado = {}
        registros_por_estado = {}

        for (cidade, estado), quantidade in contador_municipios.items():
            if estado not in municipios_por_estado:
                municipios_por_estado[estado] = 0
                registros_por_estado[estado] = 0
            municipios_por_estado[estado] += 1
            registros_por_estado[estado] += quantidade

        for estado in sorted(registros_por_estado.keys(), key=lambda x: registros_por_estado[x], reverse=True):
            f.write(f"{estado}: {municipios_por_estado[estado]} municípios, {registros_por_estado[estado]} registros\n")

        f.write(f"\nTOP 20 MUNICÍPIOS:\n")
        f.write("-" * 30 + "\n")
        for i, ((cidade, estado), quantidade) in enumerate(contador_municipios.most_common(20), 1):
            f.write(f"{i:2d}. {cidade} ({estado}): {quantidade} registros\n")

        f.write(f"\nMUNICÍPIOS POR ESTADO (DETALHADO):\n")
        f.write("-" * 40 + "\n")

        for estado in sorted(registros_por_estado.keys(), key=lambda x: registros_por_estado[x], reverse=True):
            f.write(f"\n{estado} - {municipios_por_estado[estado]} municípios:\n")
            municipios_estado = [(cidade, quantidade) for (cidade, est), quantidade in contador_municipios.items() if est == estado]
            municipios_estado.sort(key=lambda x: x[1], reverse=True)

            for cidade, quantidade in municipios_estado:
                f.write(f"  • {cidade}: {quantidade} registros\n")

        f.write(f"\nARQUIVOS GERADOS:\n")
        f.write("-" * 30 + "\n")
        f.write("1. mapa_municipios_brasil_completo.png - Mapa geral com todos os municípios\n")
        f.write("2. mapa_municipios_sudeste.png - Mapa detalhado da região Sudeste\n")
        f.write("3. analise_municipios_complementar.png - Gráficos complementares\n")
        f.write("4. relatorio_municipios_detalhado.txt - Este relatório\n")

def main():
    arquivo_entrada = "report1750796675025.csv"

    print("Analisando dados por município...")
    contador_municipios, contador_cidades, contador_estados = analisar_csv_municipios(arquivo_entrada)

    print("Criando mapa detalhado por municípios...")
    coordenadas_completas = criar_mapa_municipios_detalhado(contador_municipios, contador_cidades, contador_estados)

    print("Criando mapas regionais...")
    criar_mapas_regionais(contador_municipios, coordenadas_completas)

    print("Criando visualizações complementares...")
    criar_visualizacoes_complementares(contador_municipios, contador_cidades, contador_estados)

    print("Gerando relatório detalhado...")
    gerar_relatorio_municipios(contador_municipios, contador_cidades, contador_estados, coordenadas_completas)

    print("\n" + "=" * 60)
    print("VISUALIZAÇÕES POR MUNICÍPIOS CRIADAS COM SUCESSO!")
    print("=" * 60)

    print(f"\nResumo da análise:")
    print(f"• Total de registros: {sum(contador_municipios.values())}")
    print(f"• Municípios únicos: {len(contador_municipios)}")
    print(f"• Estados representados: {len(contador_estados)}")

    print(f"\nTop 5 municípios:")
    for i, ((cidade, estado), quantidade) in enumerate(contador_municipios.most_common(5), 1):
        print(f"  {i}. {cidade} ({estado}): {quantidade} registros")

    print(f"\nDistribuição por estado:")
    municipios_por_estado = {}
    for (cidade, estado), quantidade in contador_municipios.items():
        municipios_por_estado[estado] = municipios_por_estado.get(estado, 0) + 1

    for estado, num_municipios in sorted(municipios_por_estado.items(), key=lambda x: x[1], reverse=True):
        registros_estado = sum(quantidade for (cidade, est), quantidade in contador_municipios.items() if est == estado)
        print(f"  • {estado}: {num_municipios} municípios, {registros_estado} registros")

    print(f"\nArquivos gerados:")
    print("  - mapa_municipios_brasil_completo.png (mapa geral)")
    print("  - mapa_municipios_sudeste.png (região Sudeste detalhada)")
    print("  - analise_municipios_complementar.png (gráficos complementares)")
    print("  - relatorio_municipios_detalhado.txt (relatório completo)")

    print(f"\n💡 Dica: Use o mapa geral para visão panorâmica e o mapa do Sudeste")
    print("   para análise detalhada da região com maior concentração.")

if __name__ == "__main__":
    main()