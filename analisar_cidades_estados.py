#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para analisar cidades e estados únicos do arquivo CSV
e gerar relatório simplificado com contagens
"""

import pandas as pd
import csv
from collections import Counter

def analisar_csv(arquivo_entrada):
    """
    Analisa o arquivo CSV e retorna contagens de cidades e estados
    """
    cidades = []
    estados = []
    
    # Lê o arquivo CSV com encoding adequado
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    # Remove aspas e espaços extras
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:  # Ignora linhas vazias
                        cidades.append(cidade)
                        estados.append(estado)
    
    except UnicodeDecodeError:
        # Tenta com encoding latin-1 se utf-8 falhar
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        cidades.append(cidade)
                        estados.append(estado)
    
    # Conta as ocorrências
    contador_cidades = Counter(cidades)
    contador_estados = Counter(estados)
    
    return contador_cidades, contador_estados

def gerar_relatorio(contador_cidades, contador_estados, arquivo_saida):
    """
    Gera o relatório de saída com as contagens
    """
    with open(arquivo_saida, 'w', encoding='utf-8') as file:
        file.write("=" * 60 + "\n")
        file.write("RELATÓRIO DE ANÁLISE - CIDADES E ESTADOS\n")
        file.write("=" * 60 + "\n\n")
        
        # Resumo geral
        file.write("RESUMO GERAL:\n")
        file.write("-" * 30 + "\n")
        file.write(f"Total de registros processados: {sum(contador_cidades.values())}\n")
        file.write(f"Cidades únicas encontradas: {len(contador_cidades)}\n")
        file.write(f"Estados únicos encontrados: {len(contador_estados)}\n\n")
        
        # Contagem por estado (ordenado por quantidade)
        file.write("CONTAGEM POR ESTADO:\n")
        file.write("-" * 30 + "\n")
        for estado, quantidade in contador_estados.most_common():
            file.write(f"{estado}: {quantidade} registros\n")
        file.write("\n")
        
        # Contagem por cidade (ordenado por quantidade)
        file.write("CONTAGEM POR CIDADE:\n")
        file.write("-" * 30 + "\n")
        for cidade, quantidade in contador_cidades.most_common():
            file.write(f"{cidade}: {quantidade} registros\n")
        file.write("\n")
        
        # Top 10 cidades com mais registros
        file.write("TOP 10 CIDADES COM MAIS REGISTROS:\n")
        file.write("-" * 40 + "\n")
        for i, (cidade, quantidade) in enumerate(contador_cidades.most_common(10), 1):
            file.write(f"{i:2d}. {cidade}: {quantidade} registros\n")

def gerar_relatorio_csv(contador_cidades, contador_estados):
    """
    Gera relatórios em formato CSV para análise em planilhas
    """
    # Relatório de estados
    with open("relatorio_estados.csv", 'w', encoding='utf-8', newline='') as file:
        writer = csv.writer(file, delimiter=';')
        writer.writerow(['Estado', 'Quantidade'])
        for estado, quantidade in contador_estados.most_common():
            writer.writerow([estado, quantidade])

    # Relatório de cidades
    with open("relatorio_cidades.csv", 'w', encoding='utf-8', newline='') as file:
        writer = csv.writer(file, delimiter=';')
        writer.writerow(['Cidade', 'Quantidade'])
        for cidade, quantidade in contador_cidades.most_common():
            writer.writerow([cidade, quantidade])

def main():
    arquivo_entrada = "report1750796675025.csv"
    arquivo_saida = "relatorio_cidades_estados.txt"

    print("Analisando arquivo CSV...")
    contador_cidades, contador_estados = analisar_csv(arquivo_entrada)

    print("Gerando relatórios...")
    gerar_relatorio(contador_cidades, contador_estados, arquivo_saida)
    gerar_relatorio_csv(contador_cidades, contador_estados)

    print(f"Relatórios gerados com sucesso:")
    print(f"  - {arquivo_saida}")
    print(f"  - relatorio_estados.csv")
    print(f"  - relatorio_cidades.csv")

    # Mostra um resumo na tela
    print("\n" + "=" * 50)
    print("RESUMO DA ANÁLISE:")
    print("=" * 50)
    print(f"Total de registros: {sum(contador_cidades.values())}")
    print(f"Cidades únicas: {len(contador_cidades)}")
    print(f"Estados únicos: {len(contador_estados)}")

    print("\nEstados encontrados:")
    for estado, quantidade in contador_estados.most_common():
        print(f"  {estado}: {quantidade} registros")

    print(f"\nTop 5 cidades com mais registros:")
    for i, (cidade, quantidade) in enumerate(contador_cidades.most_common(5), 1):
        print(f"  {i}. {cidade}: {quantidade} registros")

if __name__ == "__main__":
    main()
