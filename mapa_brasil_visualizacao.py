#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar visualização no mapa do Brasil com marcadores
baseados nos dados de cidades e estados do CSV
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import csv
from collections import Counter
import json

def analisar_csv_para_mapa(arquivo_entrada):
    """
    Analisa o arquivo CSV e prepara dados para visualização no mapa
    """
    estados_dados = []
    
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        estados_dados.append(estado)
    
    except UnicodeDecodeError:
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        estados_dados.append(estado)
    
    contador_estados = Counter(estados_dados)
    return contador_estados

def criar_mapa_brasil(contador_estados):
    """
    Cria mapa do Brasil com marcadores por estado
    """
    # Mapeamento de siglas para nomes completos dos estados
    estados_nomes = {
        'AC': 'Acre', 'AL': 'Alagoas', 'AP': 'Amapá', 'AM': 'Amazonas',
        'BA': 'Bahia', 'CE': 'Ceará', 'DF': 'Distrito Federal', 'ES': 'Espírito Santo',
        'GO': 'Goiás', 'MA': 'Maranhão', 'MT': 'Mato Grosso', 'MS': 'Mato Grosso do Sul',
        'MG': 'Minas Gerais', 'PA': 'Pará', 'PB': 'Paraíba', 'PR': 'Paraná',
        'PE': 'Pernambuco', 'PI': 'Piauí', 'RJ': 'Rio de Janeiro', 'RN': 'Rio Grande do Norte',
        'RS': 'Rio Grande do Sul', 'RO': 'Rondônia', 'RR': 'Roraima', 'SC': 'Santa Catarina',
        'SP': 'São Paulo', 'SE': 'Sergipe', 'TO': 'Tocantins'
    }
    
    # Coordenadas aproximadas dos centros dos estados
    coordenadas_estados = {
        'SP': {'lat': -23.5505, 'lon': -46.6333, 'nome': 'São Paulo'},
        'SC': {'lat': -27.2423, 'lon': -50.2189, 'nome': 'Santa Catarina'},
        'MG': {'lat': -19.8157, 'lon': -43.9542, 'nome': 'Minas Gerais'},
        'PR': {'lat': -24.8936, 'lon': -51.4416, 'nome': 'Paraná'},
        'GO': {'lat': -16.6869, 'lon': -49.2648, 'nome': 'Goiás'},
        'PE': {'lat': -8.8137, 'lon': -36.9541, 'nome': 'Pernambuco'},
        'MT': {'lat': -12.6819, 'lon': -56.9211, 'nome': 'Mato Grosso'},
        'RJ': {'lat': -22.9068, 'lon': -43.1729, 'nome': 'Rio de Janeiro'},
        'MS': {'lat': -20.7722, 'lon': -54.7852, 'nome': 'Mato Grosso do Sul'},
        'CE': {'lat': -5.4984, 'lon': -39.3206, 'nome': 'Ceará'},
        'PB': {'lat': -7.2400, 'lon': -36.7820, 'nome': 'Paraíba'},
        'DF': {'lat': -15.7801, 'lon': -47.9292, 'nome': 'Distrito Federal'},
        'SE': {'lat': -10.5741, 'lon': -37.3857, 'nome': 'Sergipe'},
        'BA': {'lat': -12.5797, 'lon': -41.7007, 'nome': 'Bahia'}
    }
    
    # Prepara dados para o mapa
    dados_mapa = []
    for estado, quantidade in contador_estados.items():
        if estado in coordenadas_estados:
            dados_mapa.append({
                'estado': estado,
                'nome_estado': coordenadas_estados[estado]['nome'],
                'latitude': coordenadas_estados[estado]['lat'],
                'longitude': coordenadas_estados[estado]['lon'],
                'quantidade': quantidade,
                'tamanho': min(quantidade * 3, 100)  # Tamanho do marcador proporcional
            })
    
    df_mapa = pd.DataFrame(dados_mapa)
    
    # Cria o mapa usando Plotly
    fig = px.scatter_mapbox(
        df_mapa,
        lat="latitude",
        lon="longitude",
        size="tamanho",
        color="quantidade",
        hover_name="nome_estado",
        hover_data={"quantidade": True, "latitude": False, "longitude": False, "tamanho": False},
        color_continuous_scale="Viridis",
        size_max=50,
        zoom=4,
        center={"lat": -14.2350, "lon": -51.9253},  # Centro do Brasil
        mapbox_style="open-street-map",
        title="Distribuição de Registros por Estado - Brasil",
        labels={"quantidade": "Número de Registros"}
    )
    
    # Personaliza o layout
    fig.update_layout(
        title={
            'text': "Distribuição de Registros por Estado - Brasil",
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        font=dict(size=14),
        height=700,
        width=1000
    )
    
    return fig, df_mapa

def criar_grafico_barras(contador_estados):
    """
    Cria gráfico de barras complementar
    """
    estados = list(contador_estados.keys())
    quantidades = list(contador_estados.values())
    
    fig = go.Figure(data=[
        go.Bar(
            x=estados,
            y=quantidades,
            text=quantidades,
            textposition='auto',
            marker_color='lightblue'
        )
    ])
    
    fig.update_layout(
        title="Quantidade de Registros por Estado",
        xaxis_title="Estados",
        yaxis_title="Número de Registros",
        font=dict(size=14),
        height=500,
        width=800
    )
    
    return fig

def main():
    arquivo_entrada = "report1750796675025.csv"
    
    print("Analisando dados para visualização...")
    contador_estados = analisar_csv_para_mapa(arquivo_entrada)
    
    print("Criando mapa do Brasil...")
    fig_mapa, df_mapa = criar_mapa_brasil(contador_estados)
    
    print("Criando gráfico de barras...")
    fig_barras = criar_grafico_barras(contador_estados)
    
    # Salva os gráficos
    print("Salvando visualizações...")
    fig_mapa.write_html("mapa_brasil_registros.html")
    fig_mapa.write_image("mapa_brasil_registros.png", width=1200, height=800)
    
    fig_barras.write_html("grafico_barras_estados.html")
    fig_barras.write_image("grafico_barras_estados.png", width=1000, height=600)
    
    # Salva dados em CSV para referência
    df_mapa.to_csv("dados_mapa_estados.csv", index=False, encoding='utf-8')
    
    print("\nArquivos gerados:")
    print("  - mapa_brasil_registros.html (mapa interativo)")
    print("  - mapa_brasil_registros.png (imagem para PowerPoint)")
    print("  - grafico_barras_estados.html (gráfico interativo)")
    print("  - grafico_barras_estados.png (imagem para PowerPoint)")
    print("  - dados_mapa_estados.csv (dados do mapa)")
    
    # Mostra resumo dos dados
    print(f"\nResumo dos dados:")
    print(f"Estados com registros: {len(contador_estados)}")
    print(f"Total de registros: {sum(contador_estados.values())}")
    
    print("\nDistribuição por estado:")
    for estado, quantidade in contador_estados.most_common():
        print(f"  {estado}: {quantidade} registros")
    
    # Mostra o mapa interativo
    fig_mapa.show()

if __name__ == "__main__":
    main()
