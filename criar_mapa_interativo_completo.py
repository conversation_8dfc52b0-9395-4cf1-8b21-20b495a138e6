#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar mapa interativo COMPLETO do Brasil
"""

import csv
from collections import Counter

def analisar_dados_municipios():
    """
    Analisa o arquivo CSV
    """
    arquivo_entrada = "report1750796675025.csv"
    dados_municipios = []
    
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        dados_municipios.append((cidade, estado))
    
    except UnicodeDecodeError:
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        dados_municipios.append((cidade, estado))
    
    return Counter(dados_municipios)

def obter_coordenadas_completas():
    """
    Coordenadas completas de TODAS as cidades
    """
    return {
        # São Paulo
        ('SAO PAULO', 'SP'): {'lat': -23.5505, 'lon': -46.6333},
        ('GUARULHOS', 'SP'): {'lat': -23.4538, 'lon': -46.5333},
        ('CAMPINAS', 'SP'): {'lat': -22.9056, 'lon': -47.0608},
        ('SAO BERNARDO DO CAMPO', 'SP'): {'lat': -23.6914, 'lon': -46.5646},
        ('SAO JOSE DOS CAMPOS', 'SP'): {'lat': -23.1794, 'lon': -45.8869},
        ('SANTO ANDRE', 'SP'): {'lat': -23.6629, 'lon': -46.5383},
        ('RIBEIRAO PRETO', 'SP'): {'lat': -21.1775, 'lon': -47.8103},
        ('SOROCABA', 'SP'): {'lat': -23.5015, 'lon': -47.4526},
        ('MOJI DAS CRUZES', 'SP'): {'lat': -23.5225, 'lon': -46.1883},
        ('DIADEMA', 'SP'): {'lat': -23.6861, 'lon': -46.6228},
        ('JUNDIAI', 'SP'): {'lat': -23.1864, 'lon': -46.8842},
        ('PIRACICABA', 'SP'): {'lat': -22.7253, 'lon': -47.6492},
        ('BAURU', 'SP'): {'lat': -22.3208, 'lon': -49.0608},
        ('FRANCA', 'SP'): {'lat': -20.5386, 'lon': -47.4006},
        ('INDAIATUBA', 'SP'): {'lat': -23.0922, 'lon': -47.2181},
        ('SAO JOSE DO RIO PRETO', 'SP'): {'lat': -20.8197, 'lon': -49.3794},
        ('ARARAQUARA', 'SP'): {'lat': -21.7947, 'lon': -48.1756},
        ('AMERICANA', 'SP'): {'lat': -22.7394, 'lon': -47.3314},
        ('LIMEIRA', 'SP'): {'lat': -22.5647, 'lon': -47.4017},
        ('SUMARE', 'SP'): {'lat': -22.8219, 'lon': -47.2669},
        ('PAULINIA', 'SP'): {'lat': -22.7611, 'lon': -47.1544},
        ('SAO CAETANO DO SUL', 'SP'): {'lat': -23.6236, 'lon': -46.5547},
        ('RIO CLARO', 'SP'): {'lat': -22.4114, 'lon': -47.5614},
        ('ITAPEVI', 'SP'): {'lat': -23.5489, 'lon': -46.9342},
        ('BARUERI', 'SP'): {'lat': -23.5106, 'lon': -46.8761},
        ('SANTOS', 'SP'): {'lat': -23.9608, 'lon': -46.3331},
        ('TAUBATE', 'SP'): {'lat': -23.0264, 'lon': -45.5556},
        ('MOGI GUACU', 'SP'): {'lat': -22.3681, 'lon': -46.9431},
        ('ARARAS', 'SP'): {'lat': -22.3581, 'lon': -47.3831},
        ('JACAREI', 'SP'): {'lat': -23.3053, 'lon': -45.9658},
        ('MONTE MOR', 'SP'): {'lat': -22.9456, 'lon': -47.3156},
        ('TATUI', 'SP'): {'lat': -23.3531, 'lon': -47.8656},
        ('BOITUVA', 'SP'): {'lat': -23.2831, 'lon': -47.6731},
        ('PORTO FELIZ', 'SP'): {'lat': -23.2156, 'lon': -47.5281},
        ('ITAPOLIS', 'SP'): {'lat': -21.5931, 'lon': -48.8156},
        ('HOLAMBRA', 'SP'): {'lat': -22.6381, 'lon': -47.0531},
        ('ARTUR NOGUEIRA', 'SP'): {'lat': -22.5731, 'lon': -47.1731},
        ('PILAR DO SUL', 'SP'): {'lat': -23.8131, 'lon': -47.7131},
        ('LARANJAL PAULISTA', 'SP'): {'lat': -23.0381, 'lon': -47.8381},
        ('BATATAIS', 'SP'): {'lat': -20.8881, 'lon': -47.5881},
        ('JOSE BONIFACIO', 'SP'): {'lat': -21.0531, 'lon': -49.6881},
        ('JAGUARIUNA', 'SP'): {'lat': -22.7031, 'lon': -46.9881},
        ('ITANHAEM', 'SP'): {'lat': -24.1831, 'lon': -46.7881},
        ('ORLANDIA', 'SP'): {'lat': -20.7181, 'lon': -47.8881},
        ('SAO JOSE DO RIO PARDO', 'SP'): {'lat': -21.5931, 'lon': -46.8931},
        ('ITAPECERICA DA SERRA', 'SP'): {'lat': -23.7181, 'lon': -46.8481},
        ('LEME', 'SP'): {'lat': -22.1831, 'lon': -47.3931},
        ('CESARIO LANGE', 'SP'): {'lat': -23.2281, 'lon': -47.9531},
        ('CRUZEIRO', 'SP'): {'lat': -22.5731, 'lon': -44.9631},
        ('BERTIOGA', 'SP'): {'lat': -23.8531, 'lon': -46.1381},
        ('ITUPEVA', 'SP'): {'lat': -23.1531, 'lon': -47.0631},
        ('LOUVEIRA', 'SP'): {'lat': -23.0881, 'lon': -46.9431},
        ('ILHABELA', 'SP'): {'lat': -23.7781, 'lon': -45.3581},
        ('CAIEIRAS', 'SP'): {'lat': -23.3631, 'lon': -46.7431},
        ('CAJURU', 'SP'): {'lat': -21.2731, 'lon': -47.3031},
        ('APARECIDA', 'SP'): {'lat': -22.8481, 'lon': -45.2331},
        ('HORTOLANDIA', 'SP'): {'lat': -22.8581, 'lon': -47.2181},
        ('LINS', 'SP'): {'lat': -21.6731, 'lon': -49.7431},
        ('CERQUILHO', 'SP'): {'lat': -23.1681, 'lon': -47.7431},
        ('VALINHOS', 'SP'): {'lat': -22.9731, 'lon': -46.9981},
        ('BORBOREMA', 'SP'): {'lat': -21.6231, 'lon': -49.0731},
        ('SAO JOAO DA BOA VISTA', 'SP'): {'lat': -21.9681, 'lon': -46.7981},
        ('MOGI MIRIM', 'SP'): {'lat': -22.4331, 'lon': -46.9581},
        ('SOCORRO', 'SP'): {'lat': -22.5931, 'lon': -46.5281},
        ('SAO SEBASTIAO', 'SP'): {'lat': -23.8131, 'lon': -45.4031},
        ('LENCOIS PAULISTA', 'SP'): {'lat': -22.5981, 'lon': -48.8031},
        ('CONCHAS', 'SP'): {'lat': -23.0131, 'lon': -48.0131},
        ('ITAPIRA', 'SP'): {'lat': -22.4381, 'lon': -46.8231},
        ('VINHEDO', 'SP'): {'lat': -23.0281, 'lon': -46.9731},
        ('SALTO', 'SP'): {'lat': -23.2031, 'lon': -47.2831},
        ('ASSIS', 'SP'): {'lat': -22.6631, 'lon': -50.4131},
        ('FERNANDOPOLIS', 'SP'): {'lat': -20.2831, 'lon': -50.2431},
        ('PERUIBE', 'SP'): {'lat': -24.3181, 'lon': -46.9981},
        ('MONGAGUA', 'SP'): {'lat': -24.0931, 'lon': -46.6181},
        ('OURINHOS', 'SP'): {'lat': -22.9781, 'lon': -49.8731},
        ('ARACATUBA', 'SP'): {'lat': -21.2089, 'lon': -50.4331},
        
        # Rio de Janeiro
        ('RIO DE JANEIRO', 'RJ'): {'lat': -22.9068, 'lon': -43.1729},
        ('CAMPOS DOS GOYTACAZES', 'RJ'): {'lat': -21.7642, 'lon': -41.3297},
        ('MACAE', 'RJ'): {'lat': -22.3711, 'lon': -41.7869},
        ('RIO DAS OSTRAS', 'RJ'): {'lat': -22.5264, 'lon': -41.9456},
        
        # Minas Gerais
        ('BELO HORIZONTE', 'MG'): {'lat': -19.8157, 'lon': -43.9542},
        ('UBERLANDIA', 'MG'): {'lat': -18.9113, 'lon': -48.2622},
        ('CONTAGEM', 'MG'): {'lat': -19.9317, 'lon': -44.0536},
        ('FORMIGA', 'MG'): {'lat': -20.4644, 'lon': -45.4264},
        ('PATROCINIO', 'MG'): {'lat': -18.9431, 'lon': -46.9931},
        ('PIUMHI', 'MG'): {'lat': -20.4711, 'lon': -45.9564},
        ('SAO ROQUE DE MINAS', 'MG'): {'lat': -20.2331, 'lon': -46.3731},
        ('MONTE SIAO', 'MG'): {'lat': -22.3831, 'lon': -46.5664},
        ('OURO FINO', 'MG'): {'lat': -22.2831, 'lon': -46.3664},
        ('MONTES CLAROS', 'MG'): {'lat': -16.7353, 'lon': -43.8619},
        
        # Santa Catarina
        ('JOINVILLE', 'SC'): {'lat': -26.3044, 'lon': -48.8456},
        ('BLUMENAU', 'SC'): {'lat': -26.9194, 'lon': -49.0661},
        ('CHAPECO', 'SC'): {'lat': -27.0964, 'lon': -52.6181},
        ('RIO DO SUL', 'SC'): {'lat': -27.2142, 'lon': -49.6431},
        ('INDAIAL', 'SC'): {'lat': -26.8981, 'lon': -49.2331},
        ('SAO FRANCISCO DO SUL', 'SC'): {'lat': -26.2431, 'lon': -48.6381},
        ('ARAQUARI', 'SC'): {'lat': -26.3731, 'lon': -48.7231},
        ('PORTO BELO', 'SC'): {'lat': -27.1581, 'lon': -48.5531},
        ('POMERODE', 'SC'): {'lat': -26.7431, 'lon': -49.1781},
        ('TIMBO', 'SC'): {'lat': -26.8231, 'lon': -49.2731},
        
        # Paraná
        ('CURITIBA', 'PR'): {'lat': -25.4284, 'lon': -49.2733},
        ('LONDRINA', 'PR'): {'lat': -23.3045, 'lon': -51.1696},
        ('GUARAPUAVA', 'PR'): {'lat': -25.3842, 'lon': -51.4617},
        ('ARAPOTI', 'PR'): {'lat': -24.1581, 'lon': -49.8231},
        
        # Pernambuco
        ('RECIFE', 'PE'): {'lat': -8.0476, 'lon': -34.8770},
        ('OLINDA', 'PE'): {'lat': -8.0089, 'lon': -34.8553},
        ('JABOATAO DOS GUARARAPES', 'PE'): {'lat': -8.1120, 'lon': -35.0145},
        ('PAULISTA', 'PE'): {'lat': -7.9406, 'lon': -34.8728},
        ('ARARIPINA', 'PE'): {'lat': -7.5764, 'lon': -40.4975},
        
        # Goiás
        ('GOIANIA', 'GO'): {'lat': -16.6869, 'lon': -49.2648},
        ('ANAPOLIS', 'GO'): {'lat': -16.3281, 'lon': -48.9531},
        ('TRINDADE', 'GO'): {'lat': -16.6481, 'lon': -49.4881},
        ('CERES', 'GO'): {'lat': -15.3081, 'lon': -49.5981},
        ('SENADOR CANEDO', 'GO'): {'lat': -16.7031, 'lon': -49.0931},
        ('CATALAO', 'GO'): {'lat': -18.1681, 'lon': -47.9481},
        
        # Mato Grosso
        ('CUIABA', 'MT'): {'lat': -15.6014, 'lon': -56.0979},
        ('RONDONOPOLIS', 'MT'): {'lat': -16.4706, 'lon': -54.6364},
        ('NOVA MUTUM', 'MT'): {'lat': -13.8331, 'lon': -56.0831},
        ('CLAUDIA', 'MT'): {'lat': -11.5031, 'lon': -54.8831},
        ('TANGARA DA SERRA', 'MT'): {'lat': -14.6231, 'lon': -57.5031},
        
        # Mato Grosso do Sul
        ('CAMPO GRANDE', 'MS'): {'lat': -20.4697, 'lon': -54.6201},
        ('SAO GABRIEL DO OESTE', 'MS'): {'lat': -19.3931, 'lon': -54.5631},
        
        # Ceará
        ('FORTALEZA', 'CE'): {'lat': -3.7319, 'lon': -38.5267},
        ('COREAU', 'CE'): {'lat': -3.5431, 'lon': -40.6431},
        
        # Outras
        ('BRASILIA', 'DF'): {'lat': -15.7801, 'lon': -47.9292},
        ('JOAO PESSOA', 'PB'): {'lat': -7.1195, 'lon': -34.8450},
        ('ARACAJU', 'SE'): {'lat': -10.9472, 'lon': -37.0731},
        ('JUAZEIRO', 'BA'): {'lat': -9.4111, 'lon': -40.4986}
    }

def criar_mapa_folium_final():
    """
    Cria o mapa final com Folium - focado apenas no Brasil com contorno
    """
    import folium

    # Analisa dados
    contador_municipios = analisar_dados_municipios()
    coordenadas = obter_coordenadas_completas()

    # Cria mapa centrado no Brasil com limites geográficos
    mapa = folium.Map(
        location=[-14.2350, -51.9253],  # Centro do Brasil
        zoom_start=5,
        tiles='OpenStreetMap',
        max_bounds=True,  # Habilita limites máximos
        min_zoom=4,       # Zoom mínimo
        max_zoom=12       # Zoom máximo
    )

    # Define os limites geográficos do Brasil
    # Coordenadas aproximadas das fronteiras brasileiras
    brasil_bounds = [
        [-33.75, -73.98],  # Sudoeste (sul, oeste)
        [5.27, -34.79]     # Nordeste (norte, leste)
    ]

    # Aplica os limites ao mapa
    mapa.fit_bounds(brasil_bounds)

    # Adiciona contorno aproximado do Brasil
    # Coordenadas simplificadas do perímetro brasileiro
    contorno_brasil = [
        # Norte (Roraima)
        [5.27, -60.23],
        [4.56, -59.47],
        [2.82, -51.65],

        # Nordeste (Amapá para Ceará)
        [4.45, -51.65],
        [1.25, -51.65],
        [-0.04, -51.11],
        [-1.55, -48.45],
        [-2.59, -44.21],
        [-2.74, -40.35],
        [-4.56, -37.34],
        [-5.79, -35.20],

        # Leste (Rio Grande do Norte para Bahia)
        [-6.64, -34.79],
        [-8.05, -34.88],
        [-9.67, -35.73],
        [-11.09, -37.46],
        [-12.58, -38.51],
        [-15.75, -39.28],
        [-17.33, -39.41],

        # Sudeste (Espírito Santo para São Paulo)
        [-19.83, -39.69],
        [-20.76, -40.69],
        [-21.96, -41.59],
        [-22.91, -43.21],
        [-23.77, -45.69],
        [-25.35, -48.65],

        # Sul (Paraná para Rio Grande do Sul)
        [-26.24, -48.84],
        [-28.68, -49.69],
        [-29.69, -50.15],
        [-30.11, -51.29],
        [-31.77, -52.09],
        [-32.46, -52.37],
        [-33.75, -53.65],

        # Oeste (fronteira com Uruguai/Argentina)
        [-33.75, -57.63],
        [-32.17, -58.15],
        [-30.17, -57.63],
        [-27.44, -55.16],
        [-25.30, -54.62],
        [-22.87, -55.79],
        [-20.15, -56.47],
        [-19.68, -57.85],
        [-18.04, -57.65],
        [-16.29, -57.85],
        [-15.61, -59.52],
        [-13.69, -60.54],
        [-12.48, -63.29],
        [-11.22, -65.33],
        [-9.82, -66.87],
        [-9.39, -68.23],
        [-8.90, -70.09],
        [-7.52, -72.79],
        [-7.34, -73.98],

        # Norte (Acre para Amazonas)
        [-4.23, -73.98],
        [-2.31, -71.30],
        [-0.15, -68.78],
        [1.72, -67.34],
        [2.82, -65.59],
        [3.42, -64.63],
        [4.17, -62.73],
        [5.27, -60.23]  # Volta ao início
    ]

    # Adiciona o contorno do Brasil ao mapa
    folium.PolyLine(
        locations=contorno_brasil,
        color='#2E8B57',      # Verde escuro
        weight=3,             # Espessura da linha
        opacity=0.8,          # Transparência
        popup='Fronteiras do Brasil',
        tooltip='Perímetro brasileiro'
    ).add_to(mapa)

    # Cores por estado
    cores = {
        'SP': 'blue', 'RJ': 'red', 'MG': 'green', 'SC': 'purple',
        'PR': 'orange', 'GO': 'brown', 'PE': 'pink', 'MT': 'gray',
        'MS': 'darkblue', 'CE': 'darkgreen', 'PB': 'lightblue',
        'DF': 'darkred', 'SE': 'lightgray', 'BA': 'lightred'
    }

    # Adiciona apenas os marcadores circulares (sem estrelas de ranking)
    for (cidade, estado), quantidade in contador_municipios.items():
        if (cidade, estado) in coordenadas:
            coord = coordenadas[(cidade, estado)]
            raio = max(quantidade * 3, 8)

            folium.CircleMarker(
                location=[coord['lat'], coord['lon']],
                radius=raio,
                popup=f"<b>{cidade} ({estado})</b><br>{quantidade} registros",
                tooltip=f"{cidade}: {quantidade}",
                color='black',
                fillColor=cores.get(estado, 'gray'),
                fillOpacity=0.7,
                weight=1
            ).add_to(mapa)

    # Adiciona legenda atualizada (sem menção ao ranking)
    total_municipios = len(contador_municipios)
    total_registros = sum(contador_municipios.values())

    legenda_html = f'''
    <div style="position: fixed;
                bottom: 50px; left: 50px; width: 280px; height: 160px;
                background-color: white; border:2px solid grey; z-index:9999;
                font-size:12px; padding: 10px; border-radius: 5px;">
    <p><b>🗺️ Mapa do Brasil - Municípios</b></p>
    <p><b>Estatísticas:</b></p>
    <p>• Total de municípios: {total_municipios}</p>
    <p>• Total de registros: {total_registros}</p>
    <p>• Estados representados: 14</p>
    <p><b>Legenda:</b></p>
    <p>• Tamanho do círculo = Número de registros</p>
    <p>• Cores diferentes = Estados</p>
    <p>• Clique nos pontos para mais detalhes</p>
    </div>
    '''
    mapa.get_root().html.add_child(folium.Element(legenda_html))

    # Salva
    mapa.save("mapa_brasil_TODAS_CIDADES.html")
    print("✅ Mapa interativo LIMPO criado: mapa_brasil_TODAS_CIDADES.html")
    print("   • Removidos marcadores de ranking")
    print("   • Limitado geograficamente ao Brasil")

    return True

def main():
    print("🗺️ Criando mapa interativo com TODAS as cidades...")
    
    try:
        criar_mapa_folium_final()
        print("🎉 Sucesso! Abra o arquivo mapa_brasil_TODAS_CIDADES.html no navegador")
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    main()
