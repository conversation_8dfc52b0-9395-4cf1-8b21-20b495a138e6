#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar mapa real do Brasil com municípios plotados
usando dados geográficos reais e bibliotecas de mapeamento
"""

import pandas as pd
import matplotlib.pyplot as plt
import csv
from collections import Counter
import numpy as np

# Tenta importar bibliotecas de mapeamento
try:
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import folium
    from folium import plugins
    FOLIUM_AVAILABLE = True
except ImportError:
    FOLIUM_AVAILABLE = False

def analisar_dados_municipios(arquivo_entrada):
    """
    Analisa o arquivo CSV e prepara dados dos municípios
    """
    dados_municipios = []

    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho

            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')

                    if cidade and estado:
                        dados_municipios.append((cidade, estado))

    except UnicodeDecodeError:
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)

            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')

                    if cidade and estado:
                        dados_municipios.append((cidade, estado))

    contador_municipios = Counter(dados_municipios)
    contador_estados = Counter([estado for cidade, estado in dados_municipios])

    return contador_municipios, contador_estados

def obter_coordenadas_municipios_brasil():
    """
    Coordenadas precisas dos principais municípios brasileiros
    """
    coordenadas = {
        # São Paulo - principais cidades
        ('SAO PAULO', 'SP'): {'lat': -23.5505, 'lon': -46.6333},
        ('GUARULHOS', 'SP'): {'lat': -23.4538, 'lon': -46.5333},
        ('CAMPINAS', 'SP'): {'lat': -22.9056, 'lon': -47.0608},
        ('SAO BERNARDO DO CAMPO', 'SP'): {'lat': -23.6914, 'lon': -46.5646},
        ('SAO JOSE DOS CAMPOS', 'SP'): {'lat': -23.1794, 'lon': -45.8869},
        ('SANTO ANDRE', 'SP'): {'lat': -23.6629, 'lon': -46.5383},
        ('RIBEIRAO PRETO', 'SP'): {'lat': -21.1775, 'lon': -47.8103},
        ('OSASCO', 'SP'): {'lat': -23.5329, 'lon': -46.7918},
        ('SOROCABA', 'SP'): {'lat': -23.5015, 'lon': -47.4526},
        ('MAUÁ', 'SP'): {'lat': -23.6678, 'lon': -46.4611},
        ('SAO JOSE DO RIO PRETO', 'SP'): {'lat': -20.8197, 'lon': -49.3794},
        ('SANTOS', 'SP'): {'lat': -23.9608, 'lon': -46.3331},
        ('MOGI DAS CRUZES', 'SP'): {'lat': -23.5225, 'lon': -46.1883},
        ('DIADEMA', 'SP'): {'lat': -23.6861, 'lon': -46.6228},
        ('JUNDIAI', 'SP'): {'lat': -23.1864, 'lon': -46.8842},
        ('CARAPICUIBA', 'SP'): {'lat': -23.5225, 'lon': -46.8356},
        ('PIRACICABA', 'SP'): {'lat': -22.7253, 'lon': -47.6492},
        ('BAURU', 'SP'): {'lat': -22.3208, 'lon': -49.0608},
        ('ITAQUAQUECETUBA', 'SP'): {'lat': -23.4864, 'lon': -46.3481},
        ('SAO VICENTE', 'SP'): {'lat': -23.9631, 'lon': -46.3917},
        ('FRANCA', 'SP'): {'lat': -20.5386, 'lon': -47.4006},
        ('GUARUJA', 'SP'): {'lat': -23.9931, 'lon': -46.2564},
        ('TAUBATE', 'SP'): {'lat': -23.0264, 'lon': -45.5556},
        ('LIMEIRA', 'SP'): {'lat': -22.5647, 'lon': -47.4017},
        ('SUZANO', 'SP'): {'lat': -23.5425, 'lon': -46.3108},
        ('TABOAO DA SERRA', 'SP'): {'lat': -23.6092, 'lon': -46.7581},
        ('SUMARE', 'SP'): {'lat': -22.8219, 'lon': -47.2669},
        ('SAO CAETANO DO SUL', 'SP'): {'lat': -23.6236, 'lon': -46.5547},
        ('MOGI GUACU', 'SP'): {'lat': -22.3681, 'lon': -46.9431},
        ('AMERICANA', 'SP'): {'lat': -22.7394, 'lon': -47.3314},
        ('RIO CLARO', 'SP'): {'lat': -22.4114, 'lon': -47.5614},
        ('ARARAQUARA', 'SP'): {'lat': -21.7947, 'lon': -48.1756},
        ('INDAIATUBA', 'SP'): {'lat': -23.0922, 'lon': -47.2181},
        ('COTIA', 'SP'): {'lat': -23.6039, 'lon': -46.9189},
        ('MARILIA', 'SP'): {'lat': -22.2139, 'lon': -49.9456},
        ('SAO CARLOS', 'SP'): {'lat': -22.0175, 'lon': -47.8908},
        ('PRESIDENTE PRUDENTE', 'SP'): {'lat': -22.1256, 'lon': -51.3889},
        ('ARACATUBA', 'SP'): {'lat': -21.2089, 'lon': -50.4331},
        ('ARARAQUARA', 'SP'): {'lat': -21.7947, 'lon': -48.1756},
        ('SANTA BARBARA D\'OESTE', 'SP'): {'lat': -22.7539, 'lon': -47.4139},
        ('FERRAZ DE VASCONCELOS', 'SP'): {'lat': -23.5425, 'lon': -46.3681},
        ('FRANCISCO MORATO', 'SP'): {'lat': -23.2814, 'lon': -46.7456},
        ('ITAPEVI', 'SP'): {'lat': -23.5489, 'lon': -46.9342},
        ('ITU', 'SP'): {'lat': -23.2642, 'lon': -47.2992},
        ('BRAGANCA PAULISTA', 'SP'): {'lat': -22.9531, 'lon': -46.5431},
        ('PINDAMONHANGABA', 'SP'): {'lat': -22.9239, 'lon': -45.4619},
        ('SAO JOSE DOS CAMPOS', 'SP'): {'lat': -23.1794, 'lon': -45.8869},
        ('JACAREI', 'SP'): {'lat': -23.3053, 'lon': -45.9658},
        ('BOTUCATU', 'SP'): {'lat': -22.8856, 'lon': -48.4456},
        ('RIBEIRAO PIRES', 'SP'): {'lat': -23.7131, 'lon': -46.4131},
        ('ATIBAIA', 'SP'): {'lat': -23.1169, 'lon': -46.5500},
        ('ARARAS', 'SP'): {'lat': -22.3581, 'lon': -47.3831},
        ('ITATIBA', 'SP'): {'lat': -23.0056, 'lon': -46.8381},
        ('MATAO', 'SP'): {'lat': -21.6031, 'lon': -48.3656},
        ('PAULINIA', 'SP'): {'lat': -22.7611, 'lon': -47.1544},
        ('JANDIRA', 'SP'): {'lat': -23.5281, 'lon': -46.9031},
        ('FRANCO DA ROCHA', 'SP'): {'lat': -23.3281, 'lon': -46.7281},
        ('MONTE MOR', 'SP'): {'lat': -22.9456, 'lon': -47.3156},
        ('TATUI', 'SP'): {'lat': -23.3531, 'lon': -47.8656},
        ('BOITUVA', 'SP'): {'lat': -23.2831, 'lon': -47.6731},
        ('PORTO FELIZ', 'SP'): {'lat': -23.2156, 'lon': -47.5281},
        ('ITAPOLIS', 'SP'): {'lat': -21.5931, 'lon': -48.8156},
        ('HOLAMBRA', 'SP'): {'lat': -22.6381, 'lon': -47.0531},
        ('ARTUR NOGUEIRA', 'SP'): {'lat': -22.5731, 'lon': -47.1731},
        ('PILAR DO SUL', 'SP'): {'lat': -23.8131, 'lon': -47.7131},
        ('LARANJAL PAULISTA', 'SP'): {'lat': -23.0381, 'lon': -47.8381},
        ('BARUERI', 'SP'): {'lat': -23.5106, 'lon': -46.8761},
        ('BATATAIS', 'SP'): {'lat': -20.8881, 'lon': -47.5881},
        ('JOSE BONIFACIO', 'SP'): {'lat': -21.0531, 'lon': -49.6881},
        ('LIMEIRA', 'SP'): {'lat': -22.5647, 'lon': -47.4017},
        ('JAGUARIUNA', 'SP'): {'lat': -22.7031, 'lon': -46.9881},
        ('ITANHAEM', 'SP'): {'lat': -24.1831, 'lon': -46.7881},
        ('ORLANDIA', 'SP'): {'lat': -20.7181, 'lon': -47.8881},
        ('SAO JOSE DO RIO PARDO', 'SP'): {'lat': -21.5931, 'lon': -46.8931},
        ('ITAPECERICA DA SERRA', 'SP'): {'lat': -23.7181, 'lon': -46.8481},
        ('LEME', 'SP'): {'lat': -22.1831, 'lon': -47.3931},
        ('CESARIO LANGE', 'SP'): {'lat': -23.2281, 'lon': -47.9531},
        ('CRUZEIRO', 'SP'): {'lat': -22.5731, 'lon': -44.9631},
        ('BERTIOGA', 'SP'): {'lat': -23.8531, 'lon': -46.1381},
        ('ITUPEVA', 'SP'): {'lat': -23.1531, 'lon': -47.0631},
        ('LOUVEIRA', 'SP'): {'lat': -23.0881, 'lon': -46.9431},
        ('ILHABELA', 'SP'): {'lat': -23.7781, 'lon': -45.3581},
        ('CAIEIRAS', 'SP'): {'lat': -23.3631, 'lon': -46.7431},
        ('CAJURU', 'SP'): {'lat': -21.2731, 'lon': -47.3031},
        ('APARECIDA', 'SP'): {'lat': -22.8481, 'lon': -45.2331},
        ('ITAPEVI', 'SP'): {'lat': -23.5489, 'lon': -46.9342},
        ('HORTOLANDIA', 'SP'): {'lat': -22.8581, 'lon': -47.2181},
        ('MOGI GUACU', 'SP'): {'lat': -22.3681, 'lon': -46.9431},
        ('LINS', 'SP'): {'lat': -21.6731, 'lon': -49.7431},
        ('CERQUILHO', 'SP'): {'lat': -23.1681, 'lon': -47.7431},
        ('VALINHOS', 'SP'): {'lat': -22.9731, 'lon': -46.9981},
        ('BORBOREMA', 'SP'): {'lat': -21.6231, 'lon': -49.0731},
        ('SAO JOAO DA BOA VISTA', 'SP'): {'lat': -21.9681, 'lon': -46.7981},
        ('MOGI MIRIM', 'SP'): {'lat': -22.4331, 'lon': -46.9581},
        ('SOCORRO', 'SP'): {'lat': -22.5931, 'lon': -46.5281},
        ('SAO SEBASTIAO', 'SP'): {'lat': -23.8131, 'lon': -45.4031},
        ('LENCOIS PAULISTA', 'SP'): {'lat': -22.5981, 'lon': -48.8031},
        ('CONCHAS', 'SP'): {'lat': -23.0131, 'lon': -48.0131},
        ('ITAPIRA', 'SP'): {'lat': -22.4381, 'lon': -46.8231},
        ('VINHEDO', 'SP'): {'lat': -23.0281, 'lon': -46.9731},
        ('SALTO', 'SP'): {'lat': -23.2031, 'lon': -47.2831},
        ('ASSIS', 'SP'): {'lat': -22.6631, 'lon': -50.4131},
        ('FERNANDOPOLIS', 'SP'): {'lat': -20.2831, 'lon': -50.2431},
        ('PERUIBE', 'SP'): {'lat': -24.3181, 'lon': -46.9981},
        ('MONGAGUA', 'SP'): {'lat': -24.0931, 'lon': -46.6181},
        ('OURINHOS', 'SP'): {'lat': -22.9781, 'lon': -49.8731},

        # Rio de Janeiro
        ('RIO DE JANEIRO', 'RJ'): {'lat': -22.9068, 'lon': -43.1729},
        ('SAO GONCALO', 'RJ'): {'lat': -22.8267, 'lon': -43.0531},
        ('DUQUE DE CAXIAS', 'RJ'): {'lat': -22.7856, 'lon': -43.3117},
        ('NOVA IGUACU', 'RJ'): {'lat': -22.7592, 'lon': -43.4511},
        ('NITEROI', 'RJ'): {'lat': -22.8833, 'lon': -43.1036},
        ('BELFORD ROXO', 'RJ'): {'lat': -22.7642, 'lon': -43.3997},
        ('SAO JOAO DE MERITI', 'RJ'): {'lat': -22.8031, 'lon': -43.3728},
        ('CAMPOS DOS GOYTACAZES', 'RJ'): {'lat': -21.7642, 'lon': -41.3297},
        ('PETROPOLIS', 'RJ'): {'lat': -22.5053, 'lon': -43.1781},
        ('VOLTA REDONDA', 'RJ'): {'lat': -22.5231, 'lon': -44.1039},
        ('MAGÉ', 'RJ'): {'lat': -22.6558, 'lon': -43.0403},
        ('ITABORAI', 'RJ'): {'lat': -22.7444, 'lon': -42.8597},
        ('MACAE', 'RJ'): {'lat': -22.3711, 'lon': -41.7869},
        ('CABO FRIO', 'RJ'): {'lat': -22.8794, 'lon': -42.0186},
        ('NOVA FRIBURGO', 'RJ'): {'lat': -22.2819, 'lon': -42.5311},
        ('ANGRA DOS REIS', 'RJ'): {'lat': -23.0067, 'lon': -44.3181},
        ('BARRA MANSA', 'RJ'): {'lat': -22.5444, 'lon': -44.1731},
        ('TERESOPOLIS', 'RJ'): {'lat': -22.4125, 'lon': -42.9661},
        ('MESQUITA', 'RJ'): {'lat': -22.7831, 'lon': -43.4311},
        ('NILOPOLIS', 'RJ'): {'lat': -22.8081, 'lon': -43.4144},
        ('RIO DAS OSTRAS', 'RJ'): {'lat': -22.5264, 'lon': -41.9456},

        # Minas Gerais
        ('BELO HORIZONTE', 'MG'): {'lat': -19.8157, 'lon': -43.9542},
        ('UBERLANDIA', 'MG'): {'lat': -18.9113, 'lon': -48.2622},
        ('CONTAGEM', 'MG'): {'lat': -19.9317, 'lon': -44.0536},
        ('JUIZ DE FORA', 'MG'): {'lat': -21.7642, 'lon': -43.3503},
        ('BETIM', 'MG'): {'lat': -19.9678, 'lon': -44.1983},
        ('MONTES CLAROS', 'MG'): {'lat': -16.7353, 'lon': -43.8619},
        ('RIBEIRAO DAS NEVES', 'MG'): {'lat': -19.7667, 'lon': -44.0869},
        ('UBERABA', 'MG'): {'lat': -19.7483, 'lon': -47.9319},
        ('GOVERNADOR VALADARES', 'MG'): {'lat': -18.8511, 'lon': -41.9494},
        ('IPATINGA', 'MG'): {'lat': -19.4683, 'lon': -42.5369},
        ('SANTA LUZIA', 'MG'): {'lat': -19.7697, 'lon': -43.8514},
        ('POCOS DE CALDAS', 'MG'): {'lat': -21.7881, 'lon': -46.5614},
        ('TEOFILO OTONI', 'MG'): {'lat': -17.8594, 'lon': -41.5053},
        ('BARBACENA', 'MG'): {'lat': -21.2258, 'lon': -43.7736},
        ('SABARA', 'MG'): {'lat': -19.8831, 'lon': -43.8014},
        ('VARGINHA', 'MG'): {'lat': -21.5519, 'lon': -45.4306},
        ('CONSELHEIRO LAFAIETE', 'MG'): {'lat': -20.6597, 'lon': -43.7864},
        ('VESPASIANO', 'MG'): {'lat': -19.6919, 'lon': -43.9231},
        ('ITABIRA', 'MG'): {'lat': -19.6197, 'lon': -43.2269},
        ('ARAGUARI', 'MG'): {'lat': -18.6472, 'lon': -48.1886},
        ('PASSOS', 'MG'): {'lat': -20.7186, 'lon': -46.6097},
        ('CORONEL FABRICIANO', 'MG'): {'lat': -19.5181, 'lon': -42.6281},
        ('MURIAE', 'MG'): {'lat': -21.1306, 'lon': -42.3664},
        ('ITUIUTABA', 'MG'): {'lat': -18.9697, 'lon': -49.4647},
        ('LAVRAS', 'MG'): {'lat': -21.2453, 'lon': -45.0000},
        ('PATOS DE MINAS', 'MG'): {'lat': -18.5789, 'lon': -46.5181},
        ('POUSO ALEGRE', 'MG'): {'lat': -22.2300, 'lon': -45.9364},
        ('SANTA RITA DO SAPUCAI', 'MG'): {'lat': -22.2500, 'lon': -45.7000},
        ('SAO JOAO DEL REI', 'MG'): {'lat': -21.1364, 'lon': -44.2631},
        ('DIVINOPOLIS', 'MG'): {'lat': -20.1386, 'lon': -44.8839},
        ('FORMIGA', 'MG'): {'lat': -20.4644, 'lon': -45.4264},
        ('PATROCINIO', 'MG'): {'lat': -18.9431, 'lon': -46.9931},
        ('PIUMHI', 'MG'): {'lat': -20.4711, 'lon': -45.9564},
        ('SAO ROQUE DE MINAS', 'MG'): {'lat': -20.2331, 'lon': -46.3731},
        ('MONTE SIAO', 'MG'): {'lat': -22.3831, 'lon': -46.5664},
        ('OURO FINO', 'MG'): {'lat': -22.2831, 'lon': -46.3664}
    }

    return coordenadas

def criar_mapa_plotly_brasil(contador_municipios, contador_estados):
    """
    Cria mapa interativo do Brasil usando Plotly
    """
    if not PLOTLY_AVAILABLE:
        print("Plotly não está disponível. Instalando...")
        import subprocess
        try:
            subprocess.check_call(['pip', 'install', 'plotly'])
            import plotly.express as px
            import plotly.graph_objects as go
        except:
            print("Erro ao instalar Plotly. Pulando mapa interativo.")
            return False

    # Obtém coordenadas
    coordenadas = obter_coordenadas_municipios_brasil()

    # Prepara dados para o mapa
    dados_mapa = []
    for (cidade, estado), quantidade in contador_municipios.items():
        if (cidade, estado) in coordenadas:
            coord = coordenadas[(cidade, estado)]
            dados_mapa.append({
                'cidade': cidade,
                'estado': estado,
                'latitude': coord['lat'],
                'longitude': coord['lon'],
                'quantidade': quantidade,
                'tamanho': min(quantidade * 5, 50),  # Tamanho do marcador
                'texto': f"{cidade} ({estado})<br>{quantidade} registros"
            })

    if not dados_mapa:
        print("Nenhum município com coordenadas encontrado.")
        return False

    df_mapa = pd.DataFrame(dados_mapa)

    # Cria o mapa usando Plotly
    fig = px.scatter_mapbox(
        df_mapa,
        lat="latitude",
        lon="longitude",
        size="tamanho",
        color="quantidade",
        hover_name="cidade",
        hover_data={
            "estado": True,
            "quantidade": True,
            "latitude": False,
            "longitude": False,
            "tamanho": False
        },
        color_continuous_scale="Viridis",
        size_max=30,
        zoom=4,
        center={"lat": -14.2350, "lon": -51.9253},  # Centro do Brasil
        mapbox_style="open-street-map",
        title="Mapa do Brasil - Distribuição de Registros por Município",
        labels={"quantidade": "Número de Registros"},
        height=800,
        width=1200
    )

    # Personaliza o layout
    fig.update_layout(
        title={
            'text': "Mapa do Brasil - Distribuição de Registros por Município",
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        font=dict(size=14),
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=-14.2350, lon=-51.9253),
            zoom=4
        )
    )

    # Salva o mapa
    fig.write_html("mapa_brasil_municipios_interativo.html")
    fig.write_image("mapa_brasil_municipios_real.png", width=1400, height=1000)

    print("✓ Mapa interativo criado: mapa_brasil_municipios_interativo.html")
    print("✓ Imagem do mapa criada: mapa_brasil_municipios_real.png")

    return True

def criar_mapa_folium_brasil(contador_municipios, contador_estados):
    """
    Cria mapa interativo usando Folium (alternativa)
    """
    if not FOLIUM_AVAILABLE:
        print("Folium não está disponível. Instalando...")
        import subprocess
        try:
            subprocess.check_call(['pip', 'install', 'folium'])
            import folium
            from folium import plugins
        except:
            print("Erro ao instalar Folium. Pulando mapa Folium.")
            return False

    # Cria mapa centrado no Brasil
    mapa_brasil = folium.Map(
        location=[-14.2350, -51.9253],  # Centro do Brasil
        zoom_start=5,
        tiles='OpenStreetMap'
    )

    # Obtém coordenadas
    coordenadas = obter_coordenadas_municipios_brasil()

    # Cores por estado
    cores_estados = {
        'SP': 'blue', 'RJ': 'red', 'MG': 'green', 'SC': 'purple',
        'PR': 'orange', 'GO': 'darkred', 'PE': 'lightred', 'MT': 'beige',
        'MS': 'darkblue', 'CE': 'darkgreen', 'PB': 'cadetblue', 'DF': 'darkpurple',
        'SE': 'white', 'BA': 'pink'
    }

    # Adiciona marcadores para cada município
    for (cidade, estado), quantidade in contador_municipios.items():
        if (cidade, estado) in coordenadas:
            coord = coordenadas[(cidade, estado)]

            # Tamanho do marcador baseado na quantidade
            raio = max(quantidade * 2, 5)

            folium.CircleMarker(
                location=[coord['lat'], coord['lon']],
                radius=raio,
                popup=f"<b>{cidade} ({estado})</b><br>{quantidade} registros",
                tooltip=f"{cidade}: {quantidade}",
                color='black',
                fillColor=cores_estados.get(estado, 'gray'),
                fillOpacity=0.7,
                weight=1
            ).add_to(mapa_brasil)

    # Adiciona legenda
    legenda_html = '''
    <div style="position: fixed;
                bottom: 50px; left: 50px; width: 200px; height: 120px;
                background-color: white; border:2px solid grey; z-index:9999;
                font-size:14px; padding: 10px">
    <p><b>Legenda</b></p>
    <p>Tamanho do círculo = Número de registros</p>
    <p>Cores representam os estados</p>
    </div>
    '''
    mapa_brasil.get_root().html.add_child(folium.Element(legenda_html))

    # Salva o mapa
    mapa_brasil.save("mapa_brasil_municipios_folium.html")
    print("✓ Mapa Folium criado: mapa_brasil_municipios_folium.html")

    return True

def criar_mapa_matplotlib_brasil(contador_municipios, contador_estados):
    """
    Cria mapa usando matplotlib com contorno do Brasil
    """
    # Configuração do matplotlib
    plt.rcParams['figure.dpi'] = 300
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['font.size'] = 12

    # Obtém coordenadas
    coordenadas = obter_coordenadas_municipios_brasil()

    # Cria figura
    fig, ax = plt.subplots(figsize=(16, 12))

    # Cores por estado
    cores_estados = {
        'SP': '#1f77b4', 'RJ': '#ff7f0e', 'MG': '#2ca02c', 'SC': '#d62728',
        'PR': '#9467bd', 'GO': '#8c564b', 'PE': '#e377c2', 'MT': '#7f7f7f',
        'MS': '#bcbd22', 'CE': '#17becf', 'PB': '#ff9896', 'DF': '#c5b0d5',
        'SE': '#c49c94', 'BA': '#f7b6d3'
    }

    # Prepara dados para plotagem
    lats, lons, tamanhos, cores, labels = [], [], [], [], []

    for (cidade, estado), quantidade in contador_municipios.items():
        if (cidade, estado) in coordenadas:
            coord = coordenadas[(cidade, estado)]
            lats.append(coord['lat'])
            lons.append(coord['lon'])
            tamanhos.append(max(quantidade * 20, 30))
            cores.append(cores_estados.get(estado, '#gray'))
            labels.append(f"{cidade} ({estado}): {quantidade}")

    # Cria o scatter plot
    scatter = ax.scatter(lons, lats, s=tamanhos, c=cores, alpha=0.7,
                        edgecolors='black', linewidth=0.5)

    # Adiciona contorno aproximado do Brasil
    # Coordenadas aproximadas das fronteiras do Brasil
    brasil_lons = [-73.98, -73.98, -34.79, -34.79, -73.98]
    brasil_lats = [5.27, -33.75, -33.75, 5.27, 5.27]
    ax.plot(brasil_lons, brasil_lats, 'k-', linewidth=2, alpha=0.3)

    # Configurações do mapa
    ax.set_xlabel('Longitude', fontsize=14)
    ax.set_ylabel('Latitude', fontsize=14)
    ax.set_title('Mapa do Brasil - Distribuição de Registros por Município',
                fontsize=18, fontweight='bold', pad=20)
    ax.grid(True, alpha=0.3)

    # Define limites para o Brasil
    ax.set_xlim(-75, -32)
    ax.set_ylim(-35, 6)

    # Adiciona anotações para os top 10 municípios
    top_municipios = contador_municipios.most_common(10)
    for (cidade, estado), quantidade in top_municipios:
        if (cidade, estado) in coordenadas:
            coord = coordenadas[(cidade, estado)]
            ax.annotate(f'{cidade}\n({quantidade})',
                       (coord['lon'], coord['lat']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=9, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

    # Cria legenda dos estados
    handles = []
    labels_legenda = []
    for estado in sorted(contador_estados.keys()):
        if estado in cores_estados:
            handles.append(plt.scatter([], [], c=cores_estados[estado], s=100))
            labels_legenda.append(f"{estado} ({contador_estados[estado]} reg.)")

    ax.legend(handles, labels_legenda, title='Estados (registros)',
             loc='upper left', bbox_to_anchor=(1, 1), fontsize=10)

    # Adiciona informações estatísticas
    total_municipios = len(contador_municipios)
    total_registros = sum(contador_municipios.values())
    municipios_mapeados = len([k for k in contador_municipios.keys() if k in coordenadas])

    info_text = f"""Estatísticas:
• Total de registros: {total_registros}
• Municípios únicos: {total_municipios}
• Municípios mapeados: {municipios_mapeados}
• Estados representados: {len(contador_estados)}"""

    ax.text(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig('mapa_brasil_municipios_matplotlib.png', bbox_inches='tight',
               facecolor='white', dpi=300)
    plt.close()

    print("✓ Mapa matplotlib criado: mapa_brasil_municipios_matplotlib.png")
    return True

def gerar_relatorio_mapa_brasil(contador_municipios, contador_estados):
    """
    Gera relatório específico para o mapa do Brasil
    """
    coordenadas = obter_coordenadas_municipios_brasil()

    with open('relatorio_mapa_brasil.txt', 'w', encoding='utf-8') as f:
        f.write("=" * 70 + "\n")
        f.write("RELATÓRIO - MAPA DO BRASIL POR MUNICÍPIOS\n")
        f.write("=" * 70 + "\n\n")

        f.write("RESUMO GERAL:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total de registros: {sum(contador_municipios.values())}\n")
        f.write(f"Municípios únicos: {len(contador_municipios)}\n")
        f.write(f"Estados representados: {len(contador_estados)}\n")

        municipios_mapeados = len([k for k in contador_municipios.keys() if k in coordenadas])
        f.write(f"Municípios com coordenadas precisas: {municipios_mapeados}\n")
        f.write(f"Cobertura de mapeamento: {(municipios_mapeados/len(contador_municipios)*100):.1f}%\n\n")

        f.write("DISTRIBUIÇÃO GEOGRÁFICA:\n")
        f.write("-" * 30 + "\n")

        # Análise por região
        regioes = {
            'Sudeste': ['SP', 'RJ', 'MG', 'ES'],
            'Sul': ['SC', 'PR', 'RS'],
            'Centro-Oeste': ['GO', 'MT', 'MS', 'DF'],
            'Nordeste': ['PE', 'CE', 'PB', 'SE', 'BA', 'AL', 'RN', 'PI', 'MA'],
            'Norte': ['AM', 'PA', 'AC', 'RO', 'RR', 'AP', 'TO']
        }

        registros_por_regiao = {}
        municipios_por_regiao = {}

        for regiao, estados_regiao in regioes.items():
            registros_regiao = 0
            municipios_regiao = 0

            for (cidade, estado), quantidade in contador_municipios.items():
                if estado in estados_regiao:
                    registros_regiao += quantidade
                    municipios_regiao += 1

            if registros_regiao > 0:
                registros_por_regiao[regiao] = registros_regiao
                municipios_por_regiao[regiao] = municipios_regiao

        for regiao in sorted(registros_por_regiao.keys(), key=lambda x: registros_por_regiao[x], reverse=True):
            registros = registros_por_regiao[regiao]
            municipios = municipios_por_regiao[regiao]
            percentual = (registros / sum(contador_municipios.values())) * 100
            f.write(f"{regiao}: {municipios} municípios, {registros} registros ({percentual:.1f}%)\n")

        f.write(f"\nTOP 15 MUNICÍPIOS NO MAPA:\n")
        f.write("-" * 35 + "\n")

        top_mapeados = [(k, v) for k, v in contador_municipios.most_common() if k in coordenadas][:15]
        for i, ((cidade, estado), quantidade) in enumerate(top_mapeados, 1):
            coord = coordenadas[(cidade, estado)]
            f.write(f"{i:2d}. {cidade} ({estado}): {quantidade} registros ")
            f.write(f"[{coord['lat']:.3f}, {coord['lon']:.3f}]\n")

        f.write(f"\nMUNICÍPIOS SEM COORDENADAS PRECISAS:\n")
        f.write("-" * 40 + "\n")

        municipios_sem_coord = [(k, v) for k, v in contador_municipios.items() if k not in coordenadas]
        if municipios_sem_coord:
            for (cidade, estado), quantidade in sorted(municipios_sem_coord, key=lambda x: x[1], reverse=True):
                f.write(f"• {cidade} ({estado}): {quantidade} registros\n")
        else:
            f.write("Todos os municípios possuem coordenadas mapeadas!\n")

        f.write(f"\nARQUIVOS DE MAPA GERADOS:\n")
        f.write("-" * 30 + "\n")
        f.write("1. mapa_brasil_municipios_interativo.html - Mapa interativo (Plotly)\n")
        f.write("2. mapa_brasil_municipios_real.png - Imagem do mapa (Plotly)\n")
        f.write("3. mapa_brasil_municipios_folium.html - Mapa interativo (Folium)\n")
        f.write("4. mapa_brasil_municipios_matplotlib.png - Mapa estático (Matplotlib)\n")
        f.write("5. relatorio_mapa_brasil.txt - Este relatório\n")

        f.write(f"\nRECOMENDAÇÕES PARA APRESENTAÇÃO:\n")
        f.write("-" * 40 + "\n")
        f.write("• Use o mapa interativo HTML para apresentações dinâmicas\n")
        f.write("• Use as imagens PNG para inserir no PowerPoint\n")
        f.write("• O mapa matplotlib é ideal para impressão\n")
        f.write("• Zoom nas regiões de maior concentração para análise detalhada\n")

def main():
    arquivo_entrada = "report1750796675025.csv"

    print("🗺️  CRIANDO MAPA REAL DO BRASIL POR MUNICÍPIOS")
    print("=" * 60)

    print("\n1. Analisando dados dos municípios...")
    contador_municipios, contador_estados = analisar_dados_municipios(arquivo_entrada)

    print(f"   ✓ {len(contador_municipios)} municípios únicos encontrados")
    print(f"   ✓ {sum(contador_municipios.values())} registros totais")
    print(f"   ✓ {len(contador_estados)} estados representados")

    # Verifica cobertura de coordenadas
    coordenadas = obter_coordenadas_municipios_brasil()
    municipios_mapeados = len([k for k in contador_municipios.keys() if k in coordenadas])
    cobertura = (municipios_mapeados / len(contador_municipios)) * 100
    print(f"   ✓ {municipios_mapeados} municípios com coordenadas precisas ({cobertura:.1f}%)")

    print("\n2. Criando mapas do Brasil...")

    # Cria mapa interativo com Plotly
    print("   📍 Criando mapa interativo (Plotly)...")
    try:
        criar_mapa_plotly_brasil(contador_municipios, contador_estados)
    except Exception as e:
        print(f"   ⚠️  Erro no mapa Plotly: {e}")

    # Cria mapa com Folium
    print("   📍 Criando mapa interativo (Folium)...")
    try:
        criar_mapa_folium_brasil(contador_municipios, contador_estados)
    except Exception as e:
        print(f"   ⚠️  Erro no mapa Folium: {e}")

    # Cria mapa com Matplotlib
    print("   📍 Criando mapa estático (Matplotlib)...")
    try:
        criar_mapa_matplotlib_brasil(contador_municipios, contador_estados)
    except Exception as e:
        print(f"   ⚠️  Erro no mapa Matplotlib: {e}")

    print("\n3. Gerando relatório...")
    gerar_relatorio_mapa_brasil(contador_municipios, contador_estados)
    print("   ✓ Relatório criado: relatorio_mapa_brasil.txt")

    print("\n" + "=" * 60)
    print("🎉 MAPAS DO BRASIL CRIADOS COM SUCESSO!")
    print("=" * 60)

    print(f"\n📊 Resumo da análise:")
    print(f"   • Total de registros: {sum(contador_municipios.values())}")
    print(f"   • Municípios únicos: {len(contador_municipios)}")
    print(f"   • Estados representados: {len(contador_estados)}")
    print(f"   • Cobertura de mapeamento: {cobertura:.1f}%")

    print(f"\n🏆 Top 5 municípios:")
    for i, ((cidade, estado), quantidade) in enumerate(contador_municipios.most_common(5), 1):
        status = "📍" if (cidade, estado) in coordenadas else "❓"
        print(f"   {i}. {status} {cidade} ({estado}): {quantidade} registros")

    print(f"\n📁 Arquivos gerados:")
    arquivos = [
        "mapa_brasil_municipios_interativo.html",
        "mapa_brasil_municipios_real.png",
        "mapa_brasil_municipios_folium.html",
        "mapa_brasil_municipios_matplotlib.png",
        "relatorio_mapa_brasil.txt"
    ]

    for arquivo in arquivos:
        print(f"   • {arquivo}")

    print(f"\n💡 Dicas para uso:")
    print("   • Abra o arquivo .html no navegador para mapas interativos")
    print("   • Use as imagens .png no PowerPoint")
    print("   • O mapa matplotlib é ideal para impressão em alta qualidade")
    print("   • Faça zoom nas regiões de interesse nos mapas interativos")

if __name__ == "__main__":
    main()