#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para criar visualização do mapa do Brasil com matplotlib
baseado nos dados de cidades e estados do CSV
"""

import pandas as pd
import matplotlib.pyplot as plt
import csv
from collections import Counter
import numpy as np

def analisar_csv_para_mapa(arquivo_entrada):
    """
    Analisa o arquivo CSV e prepara dados para visualização no mapa
    """
    estados_dados = []
    
    try:
        with open(arquivo_entrada, 'r', encoding='utf-8') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)  # Pula o cabeçalho
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        estados_dados.append(estado)
    
    except UnicodeDecodeError:
        with open(arquivo_entrada, 'r', encoding='latin-1') as file:
            reader = csv.reader(file, delimiter=';')
            next(reader)
            
            for linha in reader:
                if len(linha) >= 2 and linha[0].strip() and linha[1].strip():
                    cidade = linha[0].strip().strip('"')
                    estado = linha[1].strip().strip('"')
                    
                    if cidade and estado:
                        estados_dados.append(estado)
    
    contador_estados = Counter(estados_dados)
    return contador_estados

def criar_visualizacoes(contador_estados):
    """
    Cria visualizações dos dados por estado
    """
    # Configuração do matplotlib para melhor qualidade
    plt.rcParams['figure.dpi'] = 300
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['font.size'] = 12
    
    # Dados dos estados com coordenadas aproximadas para visualização
    estados_info = {
        'SP': {'nome': 'São Paulo', 'x': 3, 'y': 6, 'regiao': 'Sudeste'},
        'SC': {'nome': 'Santa Catarina', 'x': 3, 'y': 7, 'regiao': 'Sul'},
        'MG': {'nome': 'Minas Gerais', 'x': 2, 'y': 5, 'regiao': 'Sudeste'},
        'PR': {'nome': 'Paraná', 'x': 3, 'y': 6.5, 'regiao': 'Sul'},
        'GO': {'nome': 'Goiás', 'x': 2, 'y': 4, 'regiao': 'Centro-Oeste'},
        'PE': {'nome': 'Pernambuco', 'x': 1, 'y': 2, 'regiao': 'Nordeste'},
        'MT': {'nome': 'Mato Grosso', 'x': 1, 'y': 4, 'regiao': 'Centro-Oeste'},
        'RJ': {'nome': 'Rio de Janeiro', 'x': 3, 'y': 5.5, 'regiao': 'Sudeste'},
        'MS': {'nome': 'Mato Grosso do Sul', 'x': 2, 'y': 5.5, 'regiao': 'Centro-Oeste'},
        'CE': {'nome': 'Ceará', 'x': 1, 'y': 1, 'regiao': 'Nordeste'},
        'PB': {'nome': 'Paraíba', 'x': 1.5, 'y': 1.5, 'regiao': 'Nordeste'},
        'DF': {'nome': 'Distrito Federal', 'x': 2, 'y': 4, 'regiao': 'Centro-Oeste'},
        'SE': {'nome': 'Sergipe', 'x': 1.2, 'y': 2.5, 'regiao': 'Nordeste'},
        'BA': {'nome': 'Bahia', 'x': 1.5, 'y': 3, 'regiao': 'Nordeste'}
    }
    
    # 1. Gráfico de barras horizontal
    fig1, ax1 = plt.subplots(figsize=(12, 8))
    
    estados = list(contador_estados.keys())
    quantidades = list(contador_estados.values())
    
    # Cores por região
    cores_regiao = {
        'Sudeste': '#1f77b4',
        'Sul': '#ff7f0e', 
        'Centro-Oeste': '#2ca02c',
        'Nordeste': '#d62728',
        'Norte': '#9467bd'
    }
    
    cores = [cores_regiao.get(estados_info.get(estado, {}).get('regiao', 'Norte'), '#gray') for estado in estados]
    
    bars = ax1.barh(estados, quantidades, color=cores)
    ax1.set_xlabel('Número de Registros')
    ax1.set_ylabel('Estados')
    ax1.set_title('Distribuição de Registros por Estado', fontsize=16, fontweight='bold')
    
    # Adiciona valores nas barras
    for i, (bar, quantidade) in enumerate(zip(bars, quantidades)):
        ax1.text(bar.get_width() + 1, bar.get_y() + bar.get_height()/2, 
                str(quantidade), ha='left', va='center', fontweight='bold')
    
    # Adiciona legenda das regiões
    handles = [plt.Rectangle((0,0),1,1, color=cor) for cor in cores_regiao.values()]
    labels = list(cores_regiao.keys())
    ax1.legend(handles, labels, title='Regiões', loc='lower right')
    
    plt.tight_layout()
    plt.savefig('grafico_barras_estados.png', bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 2. Mapa conceitual do Brasil
    fig2, ax2 = plt.subplots(figsize=(14, 10))
    
    # Desenha um mapa conceitual simples do Brasil
    for estado, quantidade in contador_estados.items():
        if estado in estados_info:
            info = estados_info[estado]
            # Tamanho do círculo proporcional à quantidade
            tamanho = max(quantidade * 50, 100)
            cor = cores_regiao.get(info['regiao'], '#gray')
            
            # Desenha o círculo
            circle = plt.Circle((info['x'], info['y']), 
                              radius=np.sqrt(tamanho)/20, 
                              color=cor, alpha=0.7)
            ax2.add_patch(circle)
            
            # Adiciona o texto do estado
            ax2.text(info['x'], info['y'], f"{estado}\n{quantidade}", 
                    ha='center', va='center', fontweight='bold', 
                    fontsize=10, color='white')
    
    ax2.set_xlim(0, 4)
    ax2.set_ylim(0, 8)
    ax2.set_aspect('equal')
    ax2.set_title('Mapa Conceitual - Distribuição de Registros por Estado', 
                  fontsize=16, fontweight='bold')
    ax2.axis('off')
    
    # Legenda
    handles = [plt.Circle((0,0), 0.1, color=cor) for cor in cores_regiao.values()]
    labels = list(cores_regiao.keys())
    ax2.legend(handles, labels, title='Regiões', loc='upper left', bbox_to_anchor=(0, 1))
    
    plt.tight_layout()
    plt.savefig('mapa_conceitual_brasil.png', bbox_inches='tight', facecolor='white')
    plt.close()
    
    # 3. Gráfico de pizza por região
    fig3, ax3 = plt.subplots(figsize=(10, 8))
    
    # Agrupa por região
    registros_por_regiao = {}
    for estado, quantidade in contador_estados.items():
        regiao = estados_info.get(estado, {}).get('regiao', 'Outros')
        registros_por_regiao[regiao] = registros_por_regiao.get(regiao, 0) + quantidade
    
    regioes = list(registros_por_regiao.keys())
    valores = list(registros_por_regiao.values())
    cores_pizza = [cores_regiao.get(regiao, '#gray') for regiao in regioes]
    
    wedges, texts, autotexts = ax3.pie(valores, labels=regioes, colors=cores_pizza, 
                                      autopct='%1.1f%%', startangle=90)
    
    ax3.set_title('Distribuição de Registros por Região', fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('grafico_pizza_regioes.png', bbox_inches='tight', facecolor='white')
    plt.close()
    
    return estados_info

def gerar_relatorio_visual(contador_estados, estados_info):
    """
    Gera um relatório textual para acompanhar as visualizações
    """
    with open('relatorio_visual.txt', 'w', encoding='utf-8') as f:
        f.write("=" * 60 + "\n")
        f.write("RELATÓRIO VISUAL - DISTRIBUIÇÃO GEOGRÁFICA\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("RESUMO GERAL:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Total de registros: {sum(contador_estados.values())}\n")
        f.write(f"Estados representados: {len(contador_estados)}\n\n")
        
        # Agrupa por região
        registros_por_regiao = {}
        for estado, quantidade in contador_estados.items():
            regiao = estados_info.get(estado, {}).get('regiao', 'Outros')
            registros_por_regiao[regiao] = registros_por_regiao.get(regiao, 0) + quantidade
        
        f.write("DISTRIBUIÇÃO POR REGIÃO:\n")
        f.write("-" * 30 + "\n")
        for regiao, quantidade in sorted(registros_por_regiao.items(), 
                                       key=lambda x: x[1], reverse=True):
            percentual = (quantidade / sum(contador_estados.values())) * 100
            f.write(f"{regiao}: {quantidade} registros ({percentual:.1f}%)\n")
        
        f.write("\nDETALHAMENTO POR ESTADO:\n")
        f.write("-" * 30 + "\n")
        for estado, quantidade in contador_estados.most_common():
            nome_estado = estados_info.get(estado, {}).get('nome', estado)
            regiao = estados_info.get(estado, {}).get('regiao', 'N/A')
            f.write(f"{estado} - {nome_estado} ({regiao}): {quantidade} registros\n")
        
        f.write("\nARQUIVOS GERADOS:\n")
        f.write("-" * 30 + "\n")
        f.write("1. grafico_barras_estados.png - Gráfico de barras por estado\n")
        f.write("2. mapa_conceitual_brasil.png - Mapa conceitual do Brasil\n")
        f.write("3. grafico_pizza_regioes.png - Distribuição por região\n")
        f.write("4. relatorio_visual.txt - Este relatório\n")

def main():
    arquivo_entrada = "report1750796675025.csv"
    
    print("Analisando dados para visualização...")
    contador_estados = analisar_csv_para_mapa(arquivo_entrada)
    
    print("Criando visualizações...")
    estados_info = criar_visualizacoes(contador_estados)
    
    print("Gerando relatório visual...")
    gerar_relatorio_visual(contador_estados, estados_info)
    
    print("\nVisualizações criadas com sucesso!")
    print("\nArquivos gerados:")
    print("  - grafico_barras_estados.png (para PowerPoint)")
    print("  - mapa_conceitual_brasil.png (para PowerPoint)")
    print("  - grafico_pizza_regioes.png (para PowerPoint)")
    print("  - relatorio_visual.txt (relatório detalhado)")
    
    print(f"\nResumo dos dados:")
    print(f"Total de registros: {sum(contador_estados.values())}")
    print(f"Estados representados: {len(contador_estados)}")
    
    print("\nTop 5 estados:")
    for i, (estado, quantidade) in enumerate(contador_estados.most_common(5), 1):
        print(f"  {i}. {estado}: {quantidade} registros")

if __name__ == "__main__":
    main()
