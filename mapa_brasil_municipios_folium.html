<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_484f75ba95c63d0f96f982f39b06acbb {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
</head>
<body>
    
    
    <div style="position: fixed;
                bottom: 50px; left: 50px; width: 200px; height: 120px;
                background-color: white; border:2px solid grey; z-index:9999;
                font-size:14px; padding: 10px">
    <p><b>Legenda</b></p>
    <p>Tamanho do círculo = Número de registros</p>
    <p>Cores representam os estados</p>
    </div>
    
    
            <div class="folium-map" id="map_484f75ba95c63d0f96f982f39b06acbb" ></div>
        
</body>
<script>
    
    
            var map_484f75ba95c63d0f96f982f39b06acbb = L.map(
                "map_484f75ba95c63d0f96f982f39b06acbb",
                {
                    center: [-14.235, -51.9253],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 5,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_9fa3c1fd0e082ea6d0dc2cb9e3e32bf0 = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_9fa3c1fd0e082ea6d0dc2cb9e3e32bf0.addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
            var circle_marker_47075df608c809f5f436f5447f4a6d51 = L.circleMarker(
                [-23.4538, -46.5333],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 16, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_3568d087e061f2e54b0083397f58e0d4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_81e8a62f69e955a29e33de6b7739f03b = $(`<div id="html_81e8a62f69e955a29e33de6b7739f03b" style="width: 100.0%; height: 100.0%;"><b>GUARULHOS (SP)</b><br>8 registros</div>`)[0];
                popup_3568d087e061f2e54b0083397f58e0d4.setContent(html_81e8a62f69e955a29e33de6b7739f03b);
            
        

        circle_marker_47075df608c809f5f436f5447f4a6d51.bindPopup(popup_3568d087e061f2e54b0083397f58e0d4)
        ;

        
    
    
            circle_marker_47075df608c809f5f436f5447f4a6d51.bindTooltip(
                `<div>
                     GUARULHOS: 8
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6f0b61ec755776cf3dc9c1501ad5bf2c = L.circleMarker(
                [-22.9456, -47.3156],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_13e951cae4b515e0b47b8feab9b1350f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_76dad79add3330ed8543c749d039eb48 = $(`<div id="html_76dad79add3330ed8543c749d039eb48" style="width: 100.0%; height: 100.0%;"><b>MONTE MOR (SP)</b><br>2 registros</div>`)[0];
                popup_13e951cae4b515e0b47b8feab9b1350f.setContent(html_76dad79add3330ed8543c749d039eb48);
            
        

        circle_marker_6f0b61ec755776cf3dc9c1501ad5bf2c.bindPopup(popup_13e951cae4b515e0b47b8feab9b1350f)
        ;

        
    
    
            circle_marker_6f0b61ec755776cf3dc9c1501ad5bf2c.bindTooltip(
                `<div>
                     MONTE MOR: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ca7d9d645f5371d459520dd23f5f30c7 = L.circleMarker(
                [-23.0922, -47.2181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_21c49ec27b72b07e59263ddce5969fab = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1d104f8920ca64bf89f17974cc95088b = $(`<div id="html_1d104f8920ca64bf89f17974cc95088b" style="width: 100.0%; height: 100.0%;"><b>INDAIATUBA (SP)</b><br>5 registros</div>`)[0];
                popup_21c49ec27b72b07e59263ddce5969fab.setContent(html_1d104f8920ca64bf89f17974cc95088b);
            
        

        circle_marker_ca7d9d645f5371d459520dd23f5f30c7.bindPopup(popup_21c49ec27b72b07e59263ddce5969fab)
        ;

        
    
    
            circle_marker_ca7d9d645f5371d459520dd23f5f30c7.bindTooltip(
                `<div>
                     INDAIATUBA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_11fd11dc2160d3267775ee83e321f31c = L.circleMarker(
                [-23.5015, -47.4526],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 20, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_47faf7ae5bc6d3e9f424302035ecf6ca = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_80b99880e7923d5cc9c7bf36ea040522 = $(`<div id="html_80b99880e7923d5cc9c7bf36ea040522" style="width: 100.0%; height: 100.0%;"><b>SOROCABA (SP)</b><br>10 registros</div>`)[0];
                popup_47faf7ae5bc6d3e9f424302035ecf6ca.setContent(html_80b99880e7923d5cc9c7bf36ea040522);
            
        

        circle_marker_11fd11dc2160d3267775ee83e321f31c.bindPopup(popup_47faf7ae5bc6d3e9f424302035ecf6ca)
        ;

        
    
    
            circle_marker_11fd11dc2160d3267775ee83e321f31c.bindTooltip(
                `<div>
                     SOROCABA: 10
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3af41ef4d706ee50d4bcec07be11a449 = L.circleMarker(
                [-23.2156, -47.5281],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_6bbce73735a5b604085d36664b2e129e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_72a84dd1f61f067108ecd8057a8dcdff = $(`<div id="html_72a84dd1f61f067108ecd8057a8dcdff" style="width: 100.0%; height: 100.0%;"><b>PORTO FELIZ (SP)</b><br>1 registros</div>`)[0];
                popup_6bbce73735a5b604085d36664b2e129e.setContent(html_72a84dd1f61f067108ecd8057a8dcdff);
            
        

        circle_marker_3af41ef4d706ee50d4bcec07be11a449.bindPopup(popup_6bbce73735a5b604085d36664b2e129e)
        ;

        
    
    
            circle_marker_3af41ef4d706ee50d4bcec07be11a449.bindTooltip(
                `<div>
                     PORTO FELIZ: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_590132024017a4fd5b3e0f9df0d14939 = L.circleMarker(
                [-23.3531, -47.8656],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_f42797ce867d347ddfa6816ff33559ad = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4a39cb6f492aec564201de8b6aca7ea6 = $(`<div id="html_4a39cb6f492aec564201de8b6aca7ea6" style="width: 100.0%; height: 100.0%;"><b>TATUI (SP)</b><br>2 registros</div>`)[0];
                popup_f42797ce867d347ddfa6816ff33559ad.setContent(html_4a39cb6f492aec564201de8b6aca7ea6);
            
        

        circle_marker_590132024017a4fd5b3e0f9df0d14939.bindPopup(popup_f42797ce867d347ddfa6816ff33559ad)
        ;

        
    
    
            circle_marker_590132024017a4fd5b3e0f9df0d14939.bindTooltip(
                `<div>
                     TATUI: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_58c19e339be0edee5f09241b53194775 = L.circleMarker(
                [-23.2831, -47.6731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_0321bd0f8a6c1c1bc0ceee9267adff77 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_326c8c4573c48a05175d33bc38e02e63 = $(`<div id="html_326c8c4573c48a05175d33bc38e02e63" style="width: 100.0%; height: 100.0%;"><b>BOITUVA (SP)</b><br>4 registros</div>`)[0];
                popup_0321bd0f8a6c1c1bc0ceee9267adff77.setContent(html_326c8c4573c48a05175d33bc38e02e63);
            
        

        circle_marker_58c19e339be0edee5f09241b53194775.bindPopup(popup_0321bd0f8a6c1c1bc0ceee9267adff77)
        ;

        
    
    
            circle_marker_58c19e339be0edee5f09241b53194775.bindTooltip(
                `<div>
                     BOITUVA: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_15c85b14023ab5fadaf4731158bf68d6 = L.circleMarker(
                [-22.9056, -47.0608],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 18, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_46fa78b45308bcb086abdd7ae191ab23 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_97bce0304c86d575a5daed5185872307 = $(`<div id="html_97bce0304c86d575a5daed5185872307" style="width: 100.0%; height: 100.0%;"><b>CAMPINAS (SP)</b><br>9 registros</div>`)[0];
                popup_46fa78b45308bcb086abdd7ae191ab23.setContent(html_97bce0304c86d575a5daed5185872307);
            
        

        circle_marker_15c85b14023ab5fadaf4731158bf68d6.bindPopup(popup_46fa78b45308bcb086abdd7ae191ab23)
        ;

        
    
    
            circle_marker_15c85b14023ab5fadaf4731158bf68d6.bindTooltip(
                `<div>
                     CAMPINAS: 9
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a20f9f687df44b1bb2e254e23ea0722e = L.circleMarker(
                [-21.5931, -48.8156],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_c08a90cfd18907b73275ce2456cea491 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cb248272c4aabfee450419dbada9fe06 = $(`<div id="html_cb248272c4aabfee450419dbada9fe06" style="width: 100.0%; height: 100.0%;"><b>ITAPOLIS (SP)</b><br>1 registros</div>`)[0];
                popup_c08a90cfd18907b73275ce2456cea491.setContent(html_cb248272c4aabfee450419dbada9fe06);
            
        

        circle_marker_a20f9f687df44b1bb2e254e23ea0722e.bindPopup(popup_c08a90cfd18907b73275ce2456cea491)
        ;

        
    
    
            circle_marker_a20f9f687df44b1bb2e254e23ea0722e.bindTooltip(
                `<div>
                     ITAPOLIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4da3f567ed3d297421387f127a6b7b3b = L.circleMarker(
                [-21.7947, -48.1756],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_852534efd05165b7d46f5c3f197818c6 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_545a66182a3e838357e3f1f3585a6dc7 = $(`<div id="html_545a66182a3e838357e3f1f3585a6dc7" style="width: 100.0%; height: 100.0%;"><b>ARARAQUARA (SP)</b><br>5 registros</div>`)[0];
                popup_852534efd05165b7d46f5c3f197818c6.setContent(html_545a66182a3e838357e3f1f3585a6dc7);
            
        

        circle_marker_4da3f567ed3d297421387f127a6b7b3b.bindPopup(popup_852534efd05165b7d46f5c3f197818c6)
        ;

        
    
    
            circle_marker_4da3f567ed3d297421387f127a6b7b3b.bindTooltip(
                `<div>
                     ARARAQUARA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6f417a3b9b45d70006c2fe8707656809 = L.circleMarker(
                [-23.5505, -46.6333],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 30, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_61fa32fd3842ed1211ec3535ce47ac37 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d854d2777c0734c96128c51f3d485523 = $(`<div id="html_d854d2777c0734c96128c51f3d485523" style="width: 100.0%; height: 100.0%;"><b>SAO PAULO (SP)</b><br>15 registros</div>`)[0];
                popup_61fa32fd3842ed1211ec3535ce47ac37.setContent(html_d854d2777c0734c96128c51f3d485523);
            
        

        circle_marker_6f417a3b9b45d70006c2fe8707656809.bindPopup(popup_61fa32fd3842ed1211ec3535ce47ac37)
        ;

        
    
    
            circle_marker_6f417a3b9b45d70006c2fe8707656809.bindTooltip(
                `<div>
                     SAO PAULO: 15
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_86fdd4be5e7472ad645ba917536be982 = L.circleMarker(
                [-22.7394, -47.3314],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_ea85c4f70af68f1c95fd56d079371de3 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_62cefaab10f2f1d62d481fa6ec4bcc8a = $(`<div id="html_62cefaab10f2f1d62d481fa6ec4bcc8a" style="width: 100.0%; height: 100.0%;"><b>AMERICANA (SP)</b><br>1 registros</div>`)[0];
                popup_ea85c4f70af68f1c95fd56d079371de3.setContent(html_62cefaab10f2f1d62d481fa6ec4bcc8a);
            
        

        circle_marker_86fdd4be5e7472ad645ba917536be982.bindPopup(popup_ea85c4f70af68f1c95fd56d079371de3)
        ;

        
    
    
            circle_marker_86fdd4be5e7472ad645ba917536be982.bindTooltip(
                `<div>
                     AMERICANA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4b1f99b6c160f86d52d3cf4d639ea33b = L.circleMarker(
                [-22.5731, -47.1731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_0d97d2baa7219adb4ef8f09d7fda9353 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9f186871e7f390b30eb575ca6dbba6f7 = $(`<div id="html_9f186871e7f390b30eb575ca6dbba6f7" style="width: 100.0%; height: 100.0%;"><b>ARTUR NOGUEIRA (SP)</b><br>2 registros</div>`)[0];
                popup_0d97d2baa7219adb4ef8f09d7fda9353.setContent(html_9f186871e7f390b30eb575ca6dbba6f7);
            
        

        circle_marker_4b1f99b6c160f86d52d3cf4d639ea33b.bindPopup(popup_0d97d2baa7219adb4ef8f09d7fda9353)
        ;

        
    
    
            circle_marker_4b1f99b6c160f86d52d3cf4d639ea33b.bindTooltip(
                `<div>
                     ARTUR NOGUEIRA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7e035b27daaa1daa686ddf90141e6c47 = L.circleMarker(
                [-22.6381, -47.0531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_4bc5629dc62f80a81dc8df689cd1ecc8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c6e2664aaebcba7988999c0a549781e4 = $(`<div id="html_c6e2664aaebcba7988999c0a549781e4" style="width: 100.0%; height: 100.0%;"><b>HOLAMBRA (SP)</b><br>1 registros</div>`)[0];
                popup_4bc5629dc62f80a81dc8df689cd1ecc8.setContent(html_c6e2664aaebcba7988999c0a549781e4);
            
        

        circle_marker_7e035b27daaa1daa686ddf90141e6c47.bindPopup(popup_4bc5629dc62f80a81dc8df689cd1ecc8)
        ;

        
    
    
            circle_marker_7e035b27daaa1daa686ddf90141e6c47.bindTooltip(
                `<div>
                     HOLAMBRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a01fc29dc368321bbfe1e86ac6dbbbf0 = L.circleMarker(
                [-20.2831, -50.2431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_3de5fdfb668ec1106413e1bdd01ffb18 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2fcbdda8f1ccc3f87a9a18d017666541 = $(`<div id="html_2fcbdda8f1ccc3f87a9a18d017666541" style="width: 100.0%; height: 100.0%;"><b>FERNANDOPOLIS (SP)</b><br>2 registros</div>`)[0];
                popup_3de5fdfb668ec1106413e1bdd01ffb18.setContent(html_2fcbdda8f1ccc3f87a9a18d017666541);
            
        

        circle_marker_a01fc29dc368321bbfe1e86ac6dbbbf0.bindPopup(popup_3de5fdfb668ec1106413e1bdd01ffb18)
        ;

        
    
    
            circle_marker_a01fc29dc368321bbfe1e86ac6dbbbf0.bindTooltip(
                `<div>
                     FERNANDOPOLIS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_cf78f9d9ee936442bc1f07ae6b0ead6f = L.circleMarker(
                [-22.3581, -47.3831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 6, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_a35a53e23483af82ef4af3fc8f75bd7a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_330e8a2de14a2eca25b244c80b43eae9 = $(`<div id="html_330e8a2de14a2eca25b244c80b43eae9" style="width: 100.0%; height: 100.0%;"><b>ARARAS (SP)</b><br>3 registros</div>`)[0];
                popup_a35a53e23483af82ef4af3fc8f75bd7a.setContent(html_330e8a2de14a2eca25b244c80b43eae9);
            
        

        circle_marker_cf78f9d9ee936442bc1f07ae6b0ead6f.bindPopup(popup_a35a53e23483af82ef4af3fc8f75bd7a)
        ;

        
    
    
            circle_marker_cf78f9d9ee936442bc1f07ae6b0ead6f.bindTooltip(
                `<div>
                     ARARAS: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c434e6bf0f355cc168ff4bad46a09d27 = L.circleMarker(
                [-23.1864, -46.8842],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_37020ef74cb9a58c007408e21c35b8af = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_46495ad3c6f6947f8e73514c5609b7ad = $(`<div id="html_46495ad3c6f6947f8e73514c5609b7ad" style="width: 100.0%; height: 100.0%;"><b>JUNDIAI (SP)</b><br>5 registros</div>`)[0];
                popup_37020ef74cb9a58c007408e21c35b8af.setContent(html_46495ad3c6f6947f8e73514c5609b7ad);
            
        

        circle_marker_c434e6bf0f355cc168ff4bad46a09d27.bindPopup(popup_37020ef74cb9a58c007408e21c35b8af)
        ;

        
    
    
            circle_marker_c434e6bf0f355cc168ff4bad46a09d27.bindTooltip(
                `<div>
                     JUNDIAI: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7a924b1b3e6ffb063f3f0dbd1c062a79 = L.circleMarker(
                [-23.8131, -47.7131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_19f543bcab050df04c4c514b462a3a9a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c40f1f5fc4a9ef5b1bd127d296d1fc5e = $(`<div id="html_c40f1f5fc4a9ef5b1bd127d296d1fc5e" style="width: 100.0%; height: 100.0%;"><b>PILAR DO SUL (SP)</b><br>1 registros</div>`)[0];
                popup_19f543bcab050df04c4c514b462a3a9a.setContent(html_c40f1f5fc4a9ef5b1bd127d296d1fc5e);
            
        

        circle_marker_7a924b1b3e6ffb063f3f0dbd1c062a79.bindPopup(popup_19f543bcab050df04c4c514b462a3a9a)
        ;

        
    
    
            circle_marker_7a924b1b3e6ffb063f3f0dbd1c062a79.bindTooltip(
                `<div>
                     PILAR DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_65ba9669954a92ce18b8c222eaec2465 = L.circleMarker(
                [-23.0381, -47.8381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_fede572d6402e6ae1dcb9faca6edc911 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_46329fa10c68de61ce6178b5cd6aed3f = $(`<div id="html_46329fa10c68de61ce6178b5cd6aed3f" style="width: 100.0%; height: 100.0%;"><b>LARANJAL PAULISTA (SP)</b><br>1 registros</div>`)[0];
                popup_fede572d6402e6ae1dcb9faca6edc911.setContent(html_46329fa10c68de61ce6178b5cd6aed3f);
            
        

        circle_marker_65ba9669954a92ce18b8c222eaec2465.bindPopup(popup_fede572d6402e6ae1dcb9faca6edc911)
        ;

        
    
    
            circle_marker_65ba9669954a92ce18b8c222eaec2465.bindTooltip(
                `<div>
                     LARANJAL PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4a4b392538f92a4009abd30e361d1f10 = L.circleMarker(
                [-23.5106, -46.8761],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_f06cacddeeb24a0a28b058bedd2d6034 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5ca3e098081ee69c6ef97224188e79b2 = $(`<div id="html_5ca3e098081ee69c6ef97224188e79b2" style="width: 100.0%; height: 100.0%;"><b>BARUERI (SP)</b><br>1 registros</div>`)[0];
                popup_f06cacddeeb24a0a28b058bedd2d6034.setContent(html_5ca3e098081ee69c6ef97224188e79b2);
            
        

        circle_marker_4a4b392538f92a4009abd30e361d1f10.bindPopup(popup_f06cacddeeb24a0a28b058bedd2d6034)
        ;

        
    
    
            circle_marker_4a4b392538f92a4009abd30e361d1f10.bindTooltip(
                `<div>
                     BARUERI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9e0592d5c4e7894a8eee3648c8262e25 = L.circleMarker(
                [-20.8881, -47.5881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_6a35a74c179151ffbb565c08d3293423 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0330a5e26c822c521814d9a524250eed = $(`<div id="html_0330a5e26c822c521814d9a524250eed" style="width: 100.0%; height: 100.0%;"><b>BATATAIS (SP)</b><br>1 registros</div>`)[0];
                popup_6a35a74c179151ffbb565c08d3293423.setContent(html_0330a5e26c822c521814d9a524250eed);
            
        

        circle_marker_9e0592d5c4e7894a8eee3648c8262e25.bindPopup(popup_6a35a74c179151ffbb565c08d3293423)
        ;

        
    
    
            circle_marker_9e0592d5c4e7894a8eee3648c8262e25.bindTooltip(
                `<div>
                     BATATAIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_42aab8434dee179c4af38fe7add7d580 = L.circleMarker(
                [-21.0531, -49.6881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_25d7d3ce273b6a9299667d0109958e0b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_691e98f7443883bbfc5c5ba95a4df938 = $(`<div id="html_691e98f7443883bbfc5c5ba95a4df938" style="width: 100.0%; height: 100.0%;"><b>JOSE BONIFACIO (SP)</b><br>1 registros</div>`)[0];
                popup_25d7d3ce273b6a9299667d0109958e0b.setContent(html_691e98f7443883bbfc5c5ba95a4df938);
            
        

        circle_marker_42aab8434dee179c4af38fe7add7d580.bindPopup(popup_25d7d3ce273b6a9299667d0109958e0b)
        ;

        
    
    
            circle_marker_42aab8434dee179c4af38fe7add7d580.bindTooltip(
                `<div>
                     JOSE BONIFACIO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_653e412e2bf0db8148a9baad37cd9e8e = L.circleMarker(
                [-20.5386, -47.4006],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_2986f26869031a1a7e783b5d63ef3510 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_718f014e5a18614954d5860530cf5dd9 = $(`<div id="html_718f014e5a18614954d5860530cf5dd9" style="width: 100.0%; height: 100.0%;"><b>FRANCA (SP)</b><br>2 registros</div>`)[0];
                popup_2986f26869031a1a7e783b5d63ef3510.setContent(html_718f014e5a18614954d5860530cf5dd9);
            
        

        circle_marker_653e412e2bf0db8148a9baad37cd9e8e.bindPopup(popup_2986f26869031a1a7e783b5d63ef3510)
        ;

        
    
    
            circle_marker_653e412e2bf0db8148a9baad37cd9e8e.bindTooltip(
                `<div>
                     FRANCA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_337540f07f673f8967a02880e80df447 = L.circleMarker(
                [-22.5647, -47.4017],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_a8fd664f415646e4aa7ccb9831b02f76 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0385bb94acc4ebdbcf8d6c23af64875b = $(`<div id="html_0385bb94acc4ebdbcf8d6c23af64875b" style="width: 100.0%; height: 100.0%;"><b>LIMEIRA (SP)</b><br>1 registros</div>`)[0];
                popup_a8fd664f415646e4aa7ccb9831b02f76.setContent(html_0385bb94acc4ebdbcf8d6c23af64875b);
            
        

        circle_marker_337540f07f673f8967a02880e80df447.bindPopup(popup_a8fd664f415646e4aa7ccb9831b02f76)
        ;

        
    
    
            circle_marker_337540f07f673f8967a02880e80df447.bindTooltip(
                `<div>
                     LIMEIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f9bd542b5bc3e6f9ca171bd56cacfd6d = L.circleMarker(
                [-22.7031, -46.9881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_b6db60177e9a58fde05256bbeaf3670e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_29a61fa1ea3adb1559934558c7844853 = $(`<div id="html_29a61fa1ea3adb1559934558c7844853" style="width: 100.0%; height: 100.0%;"><b>JAGUARIUNA (SP)</b><br>1 registros</div>`)[0];
                popup_b6db60177e9a58fde05256bbeaf3670e.setContent(html_29a61fa1ea3adb1559934558c7844853);
            
        

        circle_marker_f9bd542b5bc3e6f9ca171bd56cacfd6d.bindPopup(popup_b6db60177e9a58fde05256bbeaf3670e)
        ;

        
    
    
            circle_marker_f9bd542b5bc3e6f9ca171bd56cacfd6d.bindTooltip(
                `<div>
                     JAGUARIUNA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_25c6153daf67b5d505b7ee7666e76145 = L.circleMarker(
                [-23.6629, -46.5383],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_2327189f2da637996cf5cb67f5b08250 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e1f49fe7384bb1a6be3633809b865ea6 = $(`<div id="html_e1f49fe7384bb1a6be3633809b865ea6" style="width: 100.0%; height: 100.0%;"><b>SANTO ANDRE (SP)</b><br>1 registros</div>`)[0];
                popup_2327189f2da637996cf5cb67f5b08250.setContent(html_e1f49fe7384bb1a6be3633809b865ea6);
            
        

        circle_marker_25c6153daf67b5d505b7ee7666e76145.bindPopup(popup_2327189f2da637996cf5cb67f5b08250)
        ;

        
    
    
            circle_marker_25c6153daf67b5d505b7ee7666e76145.bindTooltip(
                `<div>
                     SANTO ANDRE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_e8c7a7658a472b4a0274b4ab2cde999c = L.circleMarker(
                [-23.1794, -45.8869],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 14, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_0e3837e8a4aa9a4363cbbc992b26df88 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6fb9c865dfe4ffb7546a4971c9010524 = $(`<div id="html_6fb9c865dfe4ffb7546a4971c9010524" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DOS CAMPOS (SP)</b><br>7 registros</div>`)[0];
                popup_0e3837e8a4aa9a4363cbbc992b26df88.setContent(html_6fb9c865dfe4ffb7546a4971c9010524);
            
        

        circle_marker_e8c7a7658a472b4a0274b4ab2cde999c.bindPopup(popup_0e3837e8a4aa9a4363cbbc992b26df88)
        ;

        
    
    
            circle_marker_e8c7a7658a472b4a0274b4ab2cde999c.bindTooltip(
                `<div>
                     SAO JOSE DOS CAMPOS: 7
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5d663ecf1be540b58d0ca26c4d6498a5 = L.circleMarker(
                [-22.4114, -47.5614],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_bc7f32453548334e8497370044abb709 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_90d8c3bd7e63808a83b490583bff298f = $(`<div id="html_90d8c3bd7e63808a83b490583bff298f" style="width: 100.0%; height: 100.0%;"><b>RIO CLARO (SP)</b><br>2 registros</div>`)[0];
                popup_bc7f32453548334e8497370044abb709.setContent(html_90d8c3bd7e63808a83b490583bff298f);
            
        

        circle_marker_5d663ecf1be540b58d0ca26c4d6498a5.bindPopup(popup_bc7f32453548334e8497370044abb709)
        ;

        
    
    
            circle_marker_5d663ecf1be540b58d0ca26c4d6498a5.bindTooltip(
                `<div>
                     RIO CLARO: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ce5657ba79bfff0e02c86f7bfce16c6b = L.circleMarker(
                [-24.1831, -46.7881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_039889ce3e5689897ace6746439c801d = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b063f4e0f0d64bc69c8f3b596072fbad = $(`<div id="html_b063f4e0f0d64bc69c8f3b596072fbad" style="width: 100.0%; height: 100.0%;"><b>ITANHAEM (SP)</b><br>1 registros</div>`)[0];
                popup_039889ce3e5689897ace6746439c801d.setContent(html_b063f4e0f0d64bc69c8f3b596072fbad);
            
        

        circle_marker_ce5657ba79bfff0e02c86f7bfce16c6b.bindPopup(popup_039889ce3e5689897ace6746439c801d)
        ;

        
    
    
            circle_marker_ce5657ba79bfff0e02c86f7bfce16c6b.bindTooltip(
                `<div>
                     ITANHAEM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_224aaf9fa1f9dd607c8dd7abebb4bcd1 = L.circleMarker(
                [-21.1775, -47.8103],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_b64a18a731553f4cf24da7e03c647e63 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f2259f3da817b1f484e4c0e8bb31bca5 = $(`<div id="html_f2259f3da817b1f484e4c0e8bb31bca5" style="width: 100.0%; height: 100.0%;"><b>RIBEIRAO PRETO (SP)</b><br>2 registros</div>`)[0];
                popup_b64a18a731553f4cf24da7e03c647e63.setContent(html_f2259f3da817b1f484e4c0e8bb31bca5);
            
        

        circle_marker_224aaf9fa1f9dd607c8dd7abebb4bcd1.bindPopup(popup_b64a18a731553f4cf24da7e03c647e63)
        ;

        
    
    
            circle_marker_224aaf9fa1f9dd607c8dd7abebb4bcd1.bindTooltip(
                `<div>
                     RIBEIRAO PRETO: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_1ab747bdc49893cee47188201de0699a = L.circleMarker(
                [-20.8197, -49.3794],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 6, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_92cf8acc1dc60c86ab35542c24654e32 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2032ebd2530da76f29c6c3fddf8da096 = $(`<div id="html_2032ebd2530da76f29c6c3fddf8da096" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DO RIO PRETO (SP)</b><br>3 registros</div>`)[0];
                popup_92cf8acc1dc60c86ab35542c24654e32.setContent(html_2032ebd2530da76f29c6c3fddf8da096);
            
        

        circle_marker_1ab747bdc49893cee47188201de0699a.bindPopup(popup_92cf8acc1dc60c86ab35542c24654e32)
        ;

        
    
    
            circle_marker_1ab747bdc49893cee47188201de0699a.bindTooltip(
                `<div>
                     SAO JOSE DO RIO PRETO: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7c6be5b463b8e09b8b7e3bb0f7ab1194 = L.circleMarker(
                [-23.9608, -46.3331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 6, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_062fc47bf895313bcacc997e684ffa65 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9dfb4e8bd0887c6d39a55c80c94745c5 = $(`<div id="html_9dfb4e8bd0887c6d39a55c80c94745c5" style="width: 100.0%; height: 100.0%;"><b>SANTOS (SP)</b><br>3 registros</div>`)[0];
                popup_062fc47bf895313bcacc997e684ffa65.setContent(html_9dfb4e8bd0887c6d39a55c80c94745c5);
            
        

        circle_marker_7c6be5b463b8e09b8b7e3bb0f7ab1194.bindPopup(popup_062fc47bf895313bcacc997e684ffa65)
        ;

        
    
    
            circle_marker_7c6be5b463b8e09b8b7e3bb0f7ab1194.bindTooltip(
                `<div>
                     SANTOS: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4c3383085a8510bdc6be65fdc1a985ed = L.circleMarker(
                [-24.0931, -46.6181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 6, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_b9eaf2fcb89c3247d45ba49dc2ca2d16 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fef8813a2aa6110ac14a6dc5cc83e595 = $(`<div id="html_fef8813a2aa6110ac14a6dc5cc83e595" style="width: 100.0%; height: 100.0%;"><b>MONGAGUA (SP)</b><br>3 registros</div>`)[0];
                popup_b9eaf2fcb89c3247d45ba49dc2ca2d16.setContent(html_fef8813a2aa6110ac14a6dc5cc83e595);
            
        

        circle_marker_4c3383085a8510bdc6be65fdc1a985ed.bindPopup(popup_b9eaf2fcb89c3247d45ba49dc2ca2d16)
        ;

        
    
    
            circle_marker_4c3383085a8510bdc6be65fdc1a985ed.bindTooltip(
                `<div>
                     MONGAGUA: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_09a4e61ce7a28ccb60cde23650494ce5 = L.circleMarker(
                [-24.3181, -46.9981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_1e7168a8e530ba892ad7f643efbba944 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_870123c73a2658f9687118f1acaad948 = $(`<div id="html_870123c73a2658f9687118f1acaad948" style="width: 100.0%; height: 100.0%;"><b>PERUIBE (SP)</b><br>2 registros</div>`)[0];
                popup_1e7168a8e530ba892ad7f643efbba944.setContent(html_870123c73a2658f9687118f1acaad948);
            
        

        circle_marker_09a4e61ce7a28ccb60cde23650494ce5.bindPopup(popup_1e7168a8e530ba892ad7f643efbba944)
        ;

        
    
    
            circle_marker_09a4e61ce7a28ccb60cde23650494ce5.bindTooltip(
                `<div>
                     PERUIBE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7592599523f1f0511355b7da066aa9ef = L.circleMarker(
                [-20.7181, -47.8881],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_3b197ca2e8aed1bee96c88fb1983da60 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ab1ca25d572d8f4f489134170dea627f = $(`<div id="html_ab1ca25d572d8f4f489134170dea627f" style="width: 100.0%; height: 100.0%;"><b>ORLANDIA (SP)</b><br>1 registros</div>`)[0];
                popup_3b197ca2e8aed1bee96c88fb1983da60.setContent(html_ab1ca25d572d8f4f489134170dea627f);
            
        

        circle_marker_7592599523f1f0511355b7da066aa9ef.bindPopup(popup_3b197ca2e8aed1bee96c88fb1983da60)
        ;

        
    
    
            circle_marker_7592599523f1f0511355b7da066aa9ef.bindTooltip(
                `<div>
                     ORLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f5e50de01aa7f062cfd8018a1e8ce319 = L.circleMarker(
                [-22.8219, -47.2669],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_89d3152750e26b677459b670f760b924 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_daa8a5b6159d295f8f774db77500ab6d = $(`<div id="html_daa8a5b6159d295f8f774db77500ab6d" style="width: 100.0%; height: 100.0%;"><b>SUMARE (SP)</b><br>2 registros</div>`)[0];
                popup_89d3152750e26b677459b670f760b924.setContent(html_daa8a5b6159d295f8f774db77500ab6d);
            
        

        circle_marker_f5e50de01aa7f062cfd8018a1e8ce319.bindPopup(popup_89d3152750e26b677459b670f760b924)
        ;

        
    
    
            circle_marker_f5e50de01aa7f062cfd8018a1e8ce319.bindTooltip(
                `<div>
                     SUMARE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0c5cb7b0bb1203681dab45ac6db155bc = L.circleMarker(
                [-21.5931, -46.8931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_58edb900c8d1a630e33896c153427b58 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a865a53df6082db8886fc8394ea09119 = $(`<div id="html_a865a53df6082db8886fc8394ea09119" style="width: 100.0%; height: 100.0%;"><b>SAO JOSE DO RIO PARDO (SP)</b><br>1 registros</div>`)[0];
                popup_58edb900c8d1a630e33896c153427b58.setContent(html_a865a53df6082db8886fc8394ea09119);
            
        

        circle_marker_0c5cb7b0bb1203681dab45ac6db155bc.bindPopup(popup_58edb900c8d1a630e33896c153427b58)
        ;

        
    
    
            circle_marker_0c5cb7b0bb1203681dab45ac6db155bc.bindTooltip(
                `<div>
                     SAO JOSE DO RIO PARDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_dcdf2cdb06f73b3c4fda0ff0d5f7e3d5 = L.circleMarker(
                [-23.7181, -46.8481],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_b5878421d6416bbdccaa41a47b2653c2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b6b46c90ea7828a3201504f41d8f6a00 = $(`<div id="html_b6b46c90ea7828a3201504f41d8f6a00" style="width: 100.0%; height: 100.0%;"><b>ITAPECERICA DA SERRA (SP)</b><br>1 registros</div>`)[0];
                popup_b5878421d6416bbdccaa41a47b2653c2.setContent(html_b6b46c90ea7828a3201504f41d8f6a00);
            
        

        circle_marker_dcdf2cdb06f73b3c4fda0ff0d5f7e3d5.bindPopup(popup_b5878421d6416bbdccaa41a47b2653c2)
        ;

        
    
    
            circle_marker_dcdf2cdb06f73b3c4fda0ff0d5f7e3d5.bindTooltip(
                `<div>
                     ITAPECERICA DA SERRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_88aa326b9ea65f3c6e5a029e18764174 = L.circleMarker(
                [-23.3053, -45.9658],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d5989ec035de94b8016e54e9223bc400 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2eaba59c2afd148eb4a94745209e24c1 = $(`<div id="html_2eaba59c2afd148eb4a94745209e24c1" style="width: 100.0%; height: 100.0%;"><b>JACAREI (SP)</b><br>2 registros</div>`)[0];
                popup_d5989ec035de94b8016e54e9223bc400.setContent(html_2eaba59c2afd148eb4a94745209e24c1);
            
        

        circle_marker_88aa326b9ea65f3c6e5a029e18764174.bindPopup(popup_d5989ec035de94b8016e54e9223bc400)
        ;

        
    
    
            circle_marker_88aa326b9ea65f3c6e5a029e18764174.bindTooltip(
                `<div>
                     JACAREI: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_af5f40899a2328c659d64c393d31ac00 = L.circleMarker(
                [-22.9781, -49.8731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_35210cdafa49e85d663b5fbeaf1c94c9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fb1c7f2be4edc992a7e43d1b89233664 = $(`<div id="html_fb1c7f2be4edc992a7e43d1b89233664" style="width: 100.0%; height: 100.0%;"><b>OURINHOS (SP)</b><br>2 registros</div>`)[0];
                popup_35210cdafa49e85d663b5fbeaf1c94c9.setContent(html_fb1c7f2be4edc992a7e43d1b89233664);
            
        

        circle_marker_af5f40899a2328c659d64c393d31ac00.bindPopup(popup_35210cdafa49e85d663b5fbeaf1c94c9)
        ;

        
    
    
            circle_marker_af5f40899a2328c659d64c393d31ac00.bindTooltip(
                `<div>
                     OURINHOS: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_aa577b7d74d1108b329884f019194226 = L.circleMarker(
                [-22.1831, -47.3931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_5e2b1ab1a6886ab3b99d2f167d1f6c05 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e3c4020aff20a8afa51e8cf811a99cae = $(`<div id="html_e3c4020aff20a8afa51e8cf811a99cae" style="width: 100.0%; height: 100.0%;"><b>LEME (SP)</b><br>1 registros</div>`)[0];
                popup_5e2b1ab1a6886ab3b99d2f167d1f6c05.setContent(html_e3c4020aff20a8afa51e8cf811a99cae);
            
        

        circle_marker_aa577b7d74d1108b329884f019194226.bindPopup(popup_5e2b1ab1a6886ab3b99d2f167d1f6c05)
        ;

        
    
    
            circle_marker_aa577b7d74d1108b329884f019194226.bindTooltip(
                `<div>
                     LEME: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_edf16233aa1fa4ef884c6a3175467db1 = L.circleMarker(
                [-20.4711, -45.9564],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_fedfdbe99d12a8b018bab2cb79dedadb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_b5d1e000389101e43c25239f518249b6 = $(`<div id="html_b5d1e000389101e43c25239f518249b6" style="width: 100.0%; height: 100.0%;"><b>PIUMHI (MG)</b><br>1 registros</div>`)[0];
                popup_fedfdbe99d12a8b018bab2cb79dedadb.setContent(html_b5d1e000389101e43c25239f518249b6);
            
        

        circle_marker_edf16233aa1fa4ef884c6a3175467db1.bindPopup(popup_fedfdbe99d12a8b018bab2cb79dedadb)
        ;

        
    
    
            circle_marker_edf16233aa1fa4ef884c6a3175467db1.bindTooltip(
                `<div>
                     PIUMHI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_936c6eeeab55e0105cb36526c5f19191 = L.circleMarker(
                [-23.2281, -47.9531],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_36aa8762414151a98b34b348f3f433cb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_bfb115f277a12c193eed85c1f62dd6dd = $(`<div id="html_bfb115f277a12c193eed85c1f62dd6dd" style="width: 100.0%; height: 100.0%;"><b>CESARIO LANGE (SP)</b><br>1 registros</div>`)[0];
                popup_36aa8762414151a98b34b348f3f433cb.setContent(html_bfb115f277a12c193eed85c1f62dd6dd);
            
        

        circle_marker_936c6eeeab55e0105cb36526c5f19191.bindPopup(popup_36aa8762414151a98b34b348f3f433cb)
        ;

        
    
    
            circle_marker_936c6eeeab55e0105cb36526c5f19191.bindTooltip(
                `<div>
                     CESARIO LANGE: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0be0e1f32d311c45bbafb0b5abaed61f = L.circleMarker(
                [-22.3711, -41.7869],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_465016960645375b1114ff44433368c1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7081876b8cb7a020f046199ada3dbead = $(`<div id="html_7081876b8cb7a020f046199ada3dbead" style="width: 100.0%; height: 100.0%;"><b>MACAE (RJ)</b><br>2 registros</div>`)[0];
                popup_465016960645375b1114ff44433368c1.setContent(html_7081876b8cb7a020f046199ada3dbead);
            
        

        circle_marker_0be0e1f32d311c45bbafb0b5abaed61f.bindPopup(popup_465016960645375b1114ff44433368c1)
        ;

        
    
    
            circle_marker_0be0e1f32d311c45bbafb0b5abaed61f.bindTooltip(
                `<div>
                     MACAE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_52a5638cdc0b552853763b80579e9e13 = L.circleMarker(
                [-23.0264, -45.5556],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_909e8adcb28a356f15273a221e75bcd4 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_15373261536fdd2dc2a0c3e682599c80 = $(`<div id="html_15373261536fdd2dc2a0c3e682599c80" style="width: 100.0%; height: 100.0%;"><b>TAUBATE (SP)</b><br>2 registros</div>`)[0];
                popup_909e8adcb28a356f15273a221e75bcd4.setContent(html_15373261536fdd2dc2a0c3e682599c80);
            
        

        circle_marker_52a5638cdc0b552853763b80579e9e13.bindPopup(popup_909e8adcb28a356f15273a221e75bcd4)
        ;

        
    
    
            circle_marker_52a5638cdc0b552853763b80579e9e13.bindTooltip(
                `<div>
                     TAUBATE: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c464109f20b98fb3f0800e8b2a4ee8df = L.circleMarker(
                [-22.3208, -49.0608],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 6, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_8c29b0f5615314bc88a4168bd75fea6f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ab2c1586ba3d039433a74d46280c3c6e = $(`<div id="html_ab2c1586ba3d039433a74d46280c3c6e" style="width: 100.0%; height: 100.0%;"><b>BAURU (SP)</b><br>3 registros</div>`)[0];
                popup_8c29b0f5615314bc88a4168bd75fea6f.setContent(html_ab2c1586ba3d039433a74d46280c3c6e);
            
        

        circle_marker_c464109f20b98fb3f0800e8b2a4ee8df.bindPopup(popup_8c29b0f5615314bc88a4168bd75fea6f)
        ;

        
    
    
            circle_marker_c464109f20b98fb3f0800e8b2a4ee8df.bindTooltip(
                `<div>
                     BAURU: 3
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_39adb55ac147235d783515bdb59960c3 = L.circleMarker(
                [-22.5731, -44.9631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_bf672388661d77381a2954d1950e0edb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4ef5674b37266ffb07cf9ed766db20dc = $(`<div id="html_4ef5674b37266ffb07cf9ed766db20dc" style="width: 100.0%; height: 100.0%;"><b>CRUZEIRO (SP)</b><br>1 registros</div>`)[0];
                popup_bf672388661d77381a2954d1950e0edb.setContent(html_4ef5674b37266ffb07cf9ed766db20dc);
            
        

        circle_marker_39adb55ac147235d783515bdb59960c3.bindPopup(popup_bf672388661d77381a2954d1950e0edb)
        ;

        
    
    
            circle_marker_39adb55ac147235d783515bdb59960c3.bindTooltip(
                `<div>
                     CRUZEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_93b1b63e84b9bde2e0fbd4329f42de4a = L.circleMarker(
                [-18.9113, -48.2622],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_22a24d31f71ff7180b4f80df9f0c867e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9cb1ba091c93092e7c7b8038db3e86a7 = $(`<div id="html_9cb1ba091c93092e7c7b8038db3e86a7" style="width: 100.0%; height: 100.0%;"><b>UBERLANDIA (MG)</b><br>1 registros</div>`)[0];
                popup_22a24d31f71ff7180b4f80df9f0c867e.setContent(html_9cb1ba091c93092e7c7b8038db3e86a7);
            
        

        circle_marker_93b1b63e84b9bde2e0fbd4329f42de4a.bindPopup(popup_22a24d31f71ff7180b4f80df9f0c867e)
        ;

        
    
    
            circle_marker_93b1b63e84b9bde2e0fbd4329f42de4a.bindTooltip(
                `<div>
                     UBERLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6e7420b213e12ea5d411f92b287f03c1 = L.circleMarker(
                [-23.8531, -46.1381],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_a9d39534efa697e647dfff82ebb46fd2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f7e45be139f0299a9dca00f5421f0453 = $(`<div id="html_f7e45be139f0299a9dca00f5421f0453" style="width: 100.0%; height: 100.0%;"><b>BERTIOGA (SP)</b><br>1 registros</div>`)[0];
                popup_a9d39534efa697e647dfff82ebb46fd2.setContent(html_f7e45be139f0299a9dca00f5421f0453);
            
        

        circle_marker_6e7420b213e12ea5d411f92b287f03c1.bindPopup(popup_a9d39534efa697e647dfff82ebb46fd2)
        ;

        
    
    
            circle_marker_6e7420b213e12ea5d411f92b287f03c1.bindTooltip(
                `<div>
                     BERTIOGA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_dbdb2e5c94b904bf2ce1d9e0e59e8f37 = L.circleMarker(
                [-18.9431, -46.9931],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_4f1d4686c0ed9910318be59889697322 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d7f028589535476cb6918d33055ef0d7 = $(`<div id="html_d7f028589535476cb6918d33055ef0d7" style="width: 100.0%; height: 100.0%;"><b>PATROCINIO (MG)</b><br>1 registros</div>`)[0];
                popup_4f1d4686c0ed9910318be59889697322.setContent(html_d7f028589535476cb6918d33055ef0d7);
            
        

        circle_marker_dbdb2e5c94b904bf2ce1d9e0e59e8f37.bindPopup(popup_4f1d4686c0ed9910318be59889697322)
        ;

        
    
    
            circle_marker_dbdb2e5c94b904bf2ce1d9e0e59e8f37.bindTooltip(
                `<div>
                     PATROCINIO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f31032c3145e7e7198a6d16ee51f7788 = L.circleMarker(
                [-23.1531, -47.0631],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d64a89e10d1535a914891fc720b9ec52 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a849dd201a2fa0e233725b90d42faaa0 = $(`<div id="html_a849dd201a2fa0e233725b90d42faaa0" style="width: 100.0%; height: 100.0%;"><b>ITUPEVA (SP)</b><br>1 registros</div>`)[0];
                popup_d64a89e10d1535a914891fc720b9ec52.setContent(html_a849dd201a2fa0e233725b90d42faaa0);
            
        

        circle_marker_f31032c3145e7e7198a6d16ee51f7788.bindPopup(popup_d64a89e10d1535a914891fc720b9ec52)
        ;

        
    
    
            circle_marker_f31032c3145e7e7198a6d16ee51f7788.bindTooltip(
                `<div>
                     ITUPEVA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_6920324ad7f5aff1af220228759c4c7d = L.circleMarker(
                [-23.0881, -46.9431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_bb71b3a9729329e754c608991622ceff = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ef810c7e20ca39c39569eeb809165fb3 = $(`<div id="html_ef810c7e20ca39c39569eeb809165fb3" style="width: 100.0%; height: 100.0%;"><b>LOUVEIRA (SP)</b><br>1 registros</div>`)[0];
                popup_bb71b3a9729329e754c608991622ceff.setContent(html_ef810c7e20ca39c39569eeb809165fb3);
            
        

        circle_marker_6920324ad7f5aff1af220228759c4c7d.bindPopup(popup_bb71b3a9729329e754c608991622ceff)
        ;

        
    
    
            circle_marker_6920324ad7f5aff1af220228759c4c7d.bindTooltip(
                `<div>
                     LOUVEIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_7503ec6d683a83729fc5c4fb8f24f85b = L.circleMarker(
                [-23.7781, -45.3581],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_9d18cbad5228c4dcb3dc3def1c96508a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_706ced10aa33d507cf77d53ea5adce8d = $(`<div id="html_706ced10aa33d507cf77d53ea5adce8d" style="width: 100.0%; height: 100.0%;"><b>ILHABELA (SP)</b><br>1 registros</div>`)[0];
                popup_9d18cbad5228c4dcb3dc3def1c96508a.setContent(html_706ced10aa33d507cf77d53ea5adce8d);
            
        

        circle_marker_7503ec6d683a83729fc5c4fb8f24f85b.bindPopup(popup_9d18cbad5228c4dcb3dc3def1c96508a)
        ;

        
    
    
            circle_marker_7503ec6d683a83729fc5c4fb8f24f85b.bindTooltip(
                `<div>
                     ILHABELA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_f9780246d122bd3da748ac25c9c99400 = L.circleMarker(
                [-23.3631, -46.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_eac1b9c2318372f3264cdb32e09275ca = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_10d6c0abb55986c639f8b366bd2a3abf = $(`<div id="html_10d6c0abb55986c639f8b366bd2a3abf" style="width: 100.0%; height: 100.0%;"><b>CAIEIRAS (SP)</b><br>1 registros</div>`)[0];
                popup_eac1b9c2318372f3264cdb32e09275ca.setContent(html_10d6c0abb55986c639f8b366bd2a3abf);
            
        

        circle_marker_f9780246d122bd3da748ac25c9c99400.bindPopup(popup_eac1b9c2318372f3264cdb32e09275ca)
        ;

        
    
    
            circle_marker_f9780246d122bd3da748ac25c9c99400.bindTooltip(
                `<div>
                     CAIEIRAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b28b47dec353cc01b68571565a75dbc5 = L.circleMarker(
                [-21.2731, -47.3031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_4242f689b3e5af8c1113df31fe18b116 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_392057e9fbe9700254ef0915f39b4c8e = $(`<div id="html_392057e9fbe9700254ef0915f39b4c8e" style="width: 100.0%; height: 100.0%;"><b>CAJURU (SP)</b><br>1 registros</div>`)[0];
                popup_4242f689b3e5af8c1113df31fe18b116.setContent(html_392057e9fbe9700254ef0915f39b4c8e);
            
        

        circle_marker_b28b47dec353cc01b68571565a75dbc5.bindPopup(popup_4242f689b3e5af8c1113df31fe18b116)
        ;

        
    
    
            circle_marker_b28b47dec353cc01b68571565a75dbc5.bindTooltip(
                `<div>
                     CAJURU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_50f37e6fbc7aba270d0a96fc8b0d33f7 = L.circleMarker(
                [-22.8481, -45.2331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_6ee181e44ac89d2b229904b2c98f40c2 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ca899492c66d97a9116a1b39cfd70ace = $(`<div id="html_ca899492c66d97a9116a1b39cfd70ace" style="width: 100.0%; height: 100.0%;"><b>APARECIDA (SP)</b><br>1 registros</div>`)[0];
                popup_6ee181e44ac89d2b229904b2c98f40c2.setContent(html_ca899492c66d97a9116a1b39cfd70ace);
            
        

        circle_marker_50f37e6fbc7aba270d0a96fc8b0d33f7.bindPopup(popup_6ee181e44ac89d2b229904b2c98f40c2)
        ;

        
    
    
            circle_marker_50f37e6fbc7aba270d0a96fc8b0d33f7.bindTooltip(
                `<div>
                     APARECIDA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_c972fbc4b1b579b0e05ee3b4d5ead629 = L.circleMarker(
                [-23.5489, -46.9342],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_5e60e88c2e3e5123d5cfc56aa04c7110 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_fce73a0d52601a6afdd947e0f8ec17f3 = $(`<div id="html_fce73a0d52601a6afdd947e0f8ec17f3" style="width: 100.0%; height: 100.0%;"><b>ITAPEVI (SP)</b><br>1 registros</div>`)[0];
                popup_5e60e88c2e3e5123d5cfc56aa04c7110.setContent(html_fce73a0d52601a6afdd947e0f8ec17f3);
            
        

        circle_marker_c972fbc4b1b579b0e05ee3b4d5ead629.bindPopup(popup_5e60e88c2e3e5123d5cfc56aa04c7110)
        ;

        
    
    
            circle_marker_c972fbc4b1b579b0e05ee3b4d5ead629.bindTooltip(
                `<div>
                     ITAPEVI: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_42bfd2078fd7cd6063509c7e0b424727 = L.circleMarker(
                [-22.7253, -47.6492],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_2f8bc5c914f90151568764c185f44250 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_ff6cd49151cba4641021d6c8653c1f34 = $(`<div id="html_ff6cd49151cba4641021d6c8653c1f34" style="width: 100.0%; height: 100.0%;"><b>PIRACICABA (SP)</b><br>1 registros</div>`)[0];
                popup_2f8bc5c914f90151568764c185f44250.setContent(html_ff6cd49151cba4641021d6c8653c1f34);
            
        

        circle_marker_42bfd2078fd7cd6063509c7e0b424727.bindPopup(popup_2f8bc5c914f90151568764c185f44250)
        ;

        
    
    
            circle_marker_42bfd2078fd7cd6063509c7e0b424727.bindTooltip(
                `<div>
                     PIRACICABA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_315612c49bb3048cf49f6c52c0422842 = L.circleMarker(
                [-22.8581, -47.2181],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d60e5dd05ab0f2764e50c918e7f8b9db = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e348853c9168334f03ac267e5aabff3f = $(`<div id="html_e348853c9168334f03ac267e5aabff3f" style="width: 100.0%; height: 100.0%;"><b>HORTOLANDIA (SP)</b><br>1 registros</div>`)[0];
                popup_d60e5dd05ab0f2764e50c918e7f8b9db.setContent(html_e348853c9168334f03ac267e5aabff3f);
            
        

        circle_marker_315612c49bb3048cf49f6c52c0422842.bindPopup(popup_d60e5dd05ab0f2764e50c918e7f8b9db)
        ;

        
    
    
            circle_marker_315612c49bb3048cf49f6c52c0422842.bindTooltip(
                `<div>
                     HORTOLANDIA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_98386c21a1d9ecc383e0033ccdcab461 = L.circleMarker(
                [-22.3681, -46.9431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_e08e08b6c9ad256819eeb4e5443f6505 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_2ce100a0d4caf2fc30caac5669037314 = $(`<div id="html_2ce100a0d4caf2fc30caac5669037314" style="width: 100.0%; height: 100.0%;"><b>MOGI GUACU (SP)</b><br>1 registros</div>`)[0];
                popup_e08e08b6c9ad256819eeb4e5443f6505.setContent(html_2ce100a0d4caf2fc30caac5669037314);
            
        

        circle_marker_98386c21a1d9ecc383e0033ccdcab461.bindPopup(popup_e08e08b6c9ad256819eeb4e5443f6505)
        ;

        
    
    
            circle_marker_98386c21a1d9ecc383e0033ccdcab461.bindTooltip(
                `<div>
                     MOGI GUACU: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_21fa362295c59df82ac1cb8e0f9ffc59 = L.circleMarker(
                [-21.6731, -49.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_eabf9878821d45dcfb94d90d0eeb5c42 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_255eb6fcf6154a05e86d35632705bf89 = $(`<div id="html_255eb6fcf6154a05e86d35632705bf89" style="width: 100.0%; height: 100.0%;"><b>LINS (SP)</b><br>1 registros</div>`)[0];
                popup_eabf9878821d45dcfb94d90d0eeb5c42.setContent(html_255eb6fcf6154a05e86d35632705bf89);
            
        

        circle_marker_21fa362295c59df82ac1cb8e0f9ffc59.bindPopup(popup_eabf9878821d45dcfb94d90d0eeb5c42)
        ;

        
    
    
            circle_marker_21fa362295c59df82ac1cb8e0f9ffc59.bindTooltip(
                `<div>
                     LINS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_df87ed2c6b08a7c3789d97246c1fc686 = L.circleMarker(
                [-23.1681, -47.7431],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d49ef649421f178775875220bae280a9 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_0eb2d16a874edeb5c2114a5c779b697f = $(`<div id="html_0eb2d16a874edeb5c2114a5c779b697f" style="width: 100.0%; height: 100.0%;"><b>CERQUILHO (SP)</b><br>1 registros</div>`)[0];
                popup_d49ef649421f178775875220bae280a9.setContent(html_0eb2d16a874edeb5c2114a5c779b697f);
            
        

        circle_marker_df87ed2c6b08a7c3789d97246c1fc686.bindPopup(popup_d49ef649421f178775875220bae280a9)
        ;

        
    
    
            circle_marker_df87ed2c6b08a7c3789d97246c1fc686.bindTooltip(
                `<div>
                     CERQUILHO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ffa9af5091e8bb205159174fd4030954 = L.circleMarker(
                [-22.9731, -46.9981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d074f0b0d0f0a1b2d917a094687e2b43 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_074194ae030784034e379e01a8b12a48 = $(`<div id="html_074194ae030784034e379e01a8b12a48" style="width: 100.0%; height: 100.0%;"><b>VALINHOS (SP)</b><br>1 registros</div>`)[0];
                popup_d074f0b0d0f0a1b2d917a094687e2b43.setContent(html_074194ae030784034e379e01a8b12a48);
            
        

        circle_marker_ffa9af5091e8bb205159174fd4030954.bindPopup(popup_d074f0b0d0f0a1b2d917a094687e2b43)
        ;

        
    
    
            circle_marker_ffa9af5091e8bb205159174fd4030954.bindTooltip(
                `<div>
                     VALINHOS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_ba6752f42b8e76b358d0c193fd7a3986 = L.circleMarker(
                [-21.2089, -50.4331],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_c92812d786c63a8299bbd341188dedca = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7925f66f34037a3e66cb0ec4429a4d39 = $(`<div id="html_7925f66f34037a3e66cb0ec4429a4d39" style="width: 100.0%; height: 100.0%;"><b>ARACATUBA (SP)</b><br>2 registros</div>`)[0];
                popup_c92812d786c63a8299bbd341188dedca.setContent(html_7925f66f34037a3e66cb0ec4429a4d39);
            
        

        circle_marker_ba6752f42b8e76b358d0c193fd7a3986.bindPopup(popup_c92812d786c63a8299bbd341188dedca)
        ;

        
    
    
            circle_marker_ba6752f42b8e76b358d0c193fd7a3986.bindTooltip(
                `<div>
                     ARACATUBA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_613cfa2b94cd1e1a1de6f8a44f617265 = L.circleMarker(
                [-21.7642, -41.3297],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_65c213deb336b5742ad1d3bf1c742bf1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d64a30f84a5e08151b0428029776b019 = $(`<div id="html_d64a30f84a5e08151b0428029776b019" style="width: 100.0%; height: 100.0%;"><b>CAMPOS DOS GOYTACAZES (RJ)</b><br>1 registros</div>`)[0];
                popup_65c213deb336b5742ad1d3bf1c742bf1.setContent(html_d64a30f84a5e08151b0428029776b019);
            
        

        circle_marker_613cfa2b94cd1e1a1de6f8a44f617265.bindPopup(popup_65c213deb336b5742ad1d3bf1c742bf1)
        ;

        
    
    
            circle_marker_613cfa2b94cd1e1a1de6f8a44f617265.bindTooltip(
                `<div>
                     CAMPOS DOS GOYTACAZES: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_544c01315df063078f84a86e09b33961 = L.circleMarker(
                [-21.6231, -49.0731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_c668913d570fa83198d623a9d55a2de0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_d8b0a9f02e625794e33c86145fed990f = $(`<div id="html_d8b0a9f02e625794e33c86145fed990f" style="width: 100.0%; height: 100.0%;"><b>BORBOREMA (SP)</b><br>1 registros</div>`)[0];
                popup_c668913d570fa83198d623a9d55a2de0.setContent(html_d8b0a9f02e625794e33c86145fed990f);
            
        

        circle_marker_544c01315df063078f84a86e09b33961.bindPopup(popup_c668913d570fa83198d623a9d55a2de0)
        ;

        
    
    
            circle_marker_544c01315df063078f84a86e09b33961.bindTooltip(
                `<div>
                     BORBOREMA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8aede1bc2685413118d571efc16b3af6 = L.circleMarker(
                [-23.6861, -46.6228],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_4ebd4b6d18d703352470d307185348af = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_5b9095d3425a2cfc90d23487f7619f87 = $(`<div id="html_5b9095d3425a2cfc90d23487f7619f87" style="width: 100.0%; height: 100.0%;"><b>DIADEMA (SP)</b><br>2 registros</div>`)[0];
                popup_4ebd4b6d18d703352470d307185348af.setContent(html_5b9095d3425a2cfc90d23487f7619f87);
            
        

        circle_marker_8aede1bc2685413118d571efc16b3af6.bindPopup(popup_4ebd4b6d18d703352470d307185348af)
        ;

        
    
    
            circle_marker_8aede1bc2685413118d571efc16b3af6.bindTooltip(
                `<div>
                     DIADEMA: 2
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5f840bc6b32af1f9c4f967f426e990c2 = L.circleMarker(
                [-22.7611, -47.1544],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 10, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_a734505654390fbabbee5cf8bba6f648 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_dd9b9b083af98f736ec47ef0898cb817 = $(`<div id="html_dd9b9b083af98f736ec47ef0898cb817" style="width: 100.0%; height: 100.0%;"><b>PAULINIA (SP)</b><br>5 registros</div>`)[0];
                popup_a734505654390fbabbee5cf8bba6f648.setContent(html_dd9b9b083af98f736ec47ef0898cb817);
            
        

        circle_marker_5f840bc6b32af1f9c4f967f426e990c2.bindPopup(popup_a734505654390fbabbee5cf8bba6f648)
        ;

        
    
    
            circle_marker_5f840bc6b32af1f9c4f967f426e990c2.bindTooltip(
                `<div>
                     PAULINIA: 5
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_146d988f52a163efcfb532ca18dab88f = L.circleMarker(
                [-21.9681, -46.7981],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_cf8c8119a13a18c3c41fc71d60c386d5 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_cc6fa50c51ec263bedf8ac056169c3b1 = $(`<div id="html_cc6fa50c51ec263bedf8ac056169c3b1" style="width: 100.0%; height: 100.0%;"><b>SAO JOAO DA BOA VISTA (SP)</b><br>1 registros</div>`)[0];
                popup_cf8c8119a13a18c3c41fc71d60c386d5.setContent(html_cc6fa50c51ec263bedf8ac056169c3b1);
            
        

        circle_marker_146d988f52a163efcfb532ca18dab88f.bindPopup(popup_cf8c8119a13a18c3c41fc71d60c386d5)
        ;

        
    
    
            circle_marker_146d988f52a163efcfb532ca18dab88f.bindTooltip(
                `<div>
                     SAO JOAO DA BOA VISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_9a1a8d286ecc986a6d3dc69851817dbc = L.circleMarker(
                [-22.5264, -41.9456],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_a9c8590a66687b617f5f1755cc2d43a1 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_965325c0c9b5a44ff422a25ce876fe1f = $(`<div id="html_965325c0c9b5a44ff422a25ce876fe1f" style="width: 100.0%; height: 100.0%;"><b>RIO DAS OSTRAS (RJ)</b><br>1 registros</div>`)[0];
                popup_a9c8590a66687b617f5f1755cc2d43a1.setContent(html_965325c0c9b5a44ff422a25ce876fe1f);
            
        

        circle_marker_9a1a8d286ecc986a6d3dc69851817dbc.bindPopup(popup_a9c8590a66687b617f5f1755cc2d43a1)
        ;

        
    
    
            circle_marker_9a1a8d286ecc986a6d3dc69851817dbc.bindTooltip(
                `<div>
                     RIO DAS OSTRAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5c0e5b0ec4e21e61ba6aae9f02c14f6f = L.circleMarker(
                [-22.4331, -46.9581],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_f15a8926958aba3fb005654eafbce8c0 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_6dbf580f0764e4dbe7e83a04ab512ca4 = $(`<div id="html_6dbf580f0764e4dbe7e83a04ab512ca4" style="width: 100.0%; height: 100.0%;"><b>MOGI MIRIM (SP)</b><br>1 registros</div>`)[0];
                popup_f15a8926958aba3fb005654eafbce8c0.setContent(html_6dbf580f0764e4dbe7e83a04ab512ca4);
            
        

        circle_marker_5c0e5b0ec4e21e61ba6aae9f02c14f6f.bindPopup(popup_f15a8926958aba3fb005654eafbce8c0)
        ;

        
    
    
            circle_marker_5c0e5b0ec4e21e61ba6aae9f02c14f6f.bindTooltip(
                `<div>
                     MOGI MIRIM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a88fa4d31d7d5982ff2cc4351e08e51c = L.circleMarker(
                [-22.5931, -46.5281],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_c182289f23c90f568a680efd411f2c8a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_f785d6f2fdac9c2947a4c002b1355936 = $(`<div id="html_f785d6f2fdac9c2947a4c002b1355936" style="width: 100.0%; height: 100.0%;"><b>SOCORRO (SP)</b><br>1 registros</div>`)[0];
                popup_c182289f23c90f568a680efd411f2c8a.setContent(html_f785d6f2fdac9c2947a4c002b1355936);
            
        

        circle_marker_a88fa4d31d7d5982ff2cc4351e08e51c.bindPopup(popup_c182289f23c90f568a680efd411f2c8a)
        ;

        
    
    
            circle_marker_a88fa4d31d7d5982ff2cc4351e08e51c.bindTooltip(
                `<div>
                     SOCORRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_e2e129a99fd9b8915d4c041584198d55 = L.circleMarker(
                [-19.8157, -43.9542],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 8, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_7018806fba3a8109b4f596fb9808668e = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_be00821517be2632cfa94e8284f84bd4 = $(`<div id="html_be00821517be2632cfa94e8284f84bd4" style="width: 100.0%; height: 100.0%;"><b>BELO HORIZONTE (MG)</b><br>4 registros</div>`)[0];
                popup_7018806fba3a8109b4f596fb9808668e.setContent(html_be00821517be2632cfa94e8284f84bd4);
            
        

        circle_marker_e2e129a99fd9b8915d4c041584198d55.bindPopup(popup_7018806fba3a8109b4f596fb9808668e)
        ;

        
    
    
            circle_marker_e2e129a99fd9b8915d4c041584198d55.bindTooltip(
                `<div>
                     BELO HORIZONTE: 4
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_3d0d99a4ce5e8256c1c10142a296eaa1 = L.circleMarker(
                [-22.3831, -46.5664],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_3f4e170a386fc8c86de11863f0cd840b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_c1fe717bb8412215a0e8bcd3d18e7224 = $(`<div id="html_c1fe717bb8412215a0e8bcd3d18e7224" style="width: 100.0%; height: 100.0%;"><b>MONTE SIAO (MG)</b><br>1 registros</div>`)[0];
                popup_3f4e170a386fc8c86de11863f0cd840b.setContent(html_c1fe717bb8412215a0e8bcd3d18e7224);
            
        

        circle_marker_3d0d99a4ce5e8256c1c10142a296eaa1.bindPopup(popup_3f4e170a386fc8c86de11863f0cd840b)
        ;

        
    
    
            circle_marker_3d0d99a4ce5e8256c1c10142a296eaa1.bindTooltip(
                `<div>
                     MONTE SIAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_39b8408fddc1b57e5a26cd802e1782fe = L.circleMarker(
                [-23.6914, -46.5646],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d0fc5a8dd0f9359ab5b7eaf811d25b67 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_4f7d7f5d168f3366add337adc5142ce8 = $(`<div id="html_4f7d7f5d168f3366add337adc5142ce8" style="width: 100.0%; height: 100.0%;"><b>SAO BERNARDO DO CAMPO (SP)</b><br>1 registros</div>`)[0];
                popup_d0fc5a8dd0f9359ab5b7eaf811d25b67.setContent(html_4f7d7f5d168f3366add337adc5142ce8);
            
        

        circle_marker_39b8408fddc1b57e5a26cd802e1782fe.bindPopup(popup_d0fc5a8dd0f9359ab5b7eaf811d25b67)
        ;

        
    
    
            circle_marker_39b8408fddc1b57e5a26cd802e1782fe.bindTooltip(
                `<div>
                     SAO BERNARDO DO CAMPO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_51398e0a512e155045c2b4264e54e343 = L.circleMarker(
                [-23.6236, -46.5547],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_c4c5f70e31cb73d4198549a0c9e2f8cf = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_bf4324fc48ac5603b14f4823192d3057 = $(`<div id="html_bf4324fc48ac5603b14f4823192d3057" style="width: 100.0%; height: 100.0%;"><b>SAO CAETANO DO SUL (SP)</b><br>1 registros</div>`)[0];
                popup_c4c5f70e31cb73d4198549a0c9e2f8cf.setContent(html_bf4324fc48ac5603b14f4823192d3057);
            
        

        circle_marker_51398e0a512e155045c2b4264e54e343.bindPopup(popup_c4c5f70e31cb73d4198549a0c9e2f8cf)
        ;

        
    
    
            circle_marker_51398e0a512e155045c2b4264e54e343.bindTooltip(
                `<div>
                     SAO CAETANO DO SUL: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_a0078b720f1f59cbe34e7cec9143dc14 = L.circleMarker(
                [-23.8131, -45.4031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_701ec2bc76458f25ee67132d52cb1ace = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e7103d05aadc91c0d9cda491a5d04a6b = $(`<div id="html_e7103d05aadc91c0d9cda491a5d04a6b" style="width: 100.0%; height: 100.0%;"><b>SAO SEBASTIAO (SP)</b><br>1 registros</div>`)[0];
                popup_701ec2bc76458f25ee67132d52cb1ace.setContent(html_e7103d05aadc91c0d9cda491a5d04a6b);
            
        

        circle_marker_a0078b720f1f59cbe34e7cec9143dc14.bindPopup(popup_701ec2bc76458f25ee67132d52cb1ace)
        ;

        
    
    
            circle_marker_a0078b720f1f59cbe34e7cec9143dc14.bindTooltip(
                `<div>
                     SAO SEBASTIAO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_8e008870a5e49519b2efef8dd0eccba0 = L.circleMarker(
                [-22.5981, -48.8031],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_d8a82b831e3e12e1de4aa4b31109278a = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_eef697a383b14755efc38bdb5ccef991 = $(`<div id="html_eef697a383b14755efc38bdb5ccef991" style="width: 100.0%; height: 100.0%;"><b>LENCOIS PAULISTA (SP)</b><br>1 registros</div>`)[0];
                popup_d8a82b831e3e12e1de4aa4b31109278a.setContent(html_eef697a383b14755efc38bdb5ccef991);
            
        

        circle_marker_8e008870a5e49519b2efef8dd0eccba0.bindPopup(popup_d8a82b831e3e12e1de4aa4b31109278a)
        ;

        
    
    
            circle_marker_8e008870a5e49519b2efef8dd0eccba0.bindTooltip(
                `<div>
                     LENCOIS PAULISTA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_af97d8d5a092d46dd0dcf1ea45d04fd8 = L.circleMarker(
                [-22.2831, -46.3664],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_8f6baf1bd9f67a3a98c47297387f575b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_e3c22938fae75dcb9737dc78a0e69501 = $(`<div id="html_e3c22938fae75dcb9737dc78a0e69501" style="width: 100.0%; height: 100.0%;"><b>OURO FINO (MG)</b><br>1 registros</div>`)[0];
                popup_8f6baf1bd9f67a3a98c47297387f575b.setContent(html_e3c22938fae75dcb9737dc78a0e69501);
            
        

        circle_marker_af97d8d5a092d46dd0dcf1ea45d04fd8.bindPopup(popup_8f6baf1bd9f67a3a98c47297387f575b)
        ;

        
    
    
            circle_marker_af97d8d5a092d46dd0dcf1ea45d04fd8.bindTooltip(
                `<div>
                     OURO FINO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_2efc620cf9b1907d2c26b3d8bcb16e12 = L.circleMarker(
                [-23.0131, -48.0131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_8a1e211d8a8c0a64915a7a4500352d90 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_247de814c27deb96311cf692847c8e7d = $(`<div id="html_247de814c27deb96311cf692847c8e7d" style="width: 100.0%; height: 100.0%;"><b>CONCHAS (SP)</b><br>1 registros</div>`)[0];
                popup_8a1e211d8a8c0a64915a7a4500352d90.setContent(html_247de814c27deb96311cf692847c8e7d);
            
        

        circle_marker_2efc620cf9b1907d2c26b3d8bcb16e12.bindPopup(popup_8a1e211d8a8c0a64915a7a4500352d90)
        ;

        
    
    
            circle_marker_2efc620cf9b1907d2c26b3d8bcb16e12.bindTooltip(
                `<div>
                     CONCHAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_4146ec795c12c92b1b820acf6e5d5479 = L.circleMarker(
                [-22.4381, -46.8231],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_8020cfb6c785b6f1d89cd19c546b662b = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_509e384e932a165f789c5ce50e28765a = $(`<div id="html_509e384e932a165f789c5ce50e28765a" style="width: 100.0%; height: 100.0%;"><b>ITAPIRA (SP)</b><br>1 registros</div>`)[0];
                popup_8020cfb6c785b6f1d89cd19c546b662b.setContent(html_509e384e932a165f789c5ce50e28765a);
            
        

        circle_marker_4146ec795c12c92b1b820acf6e5d5479.bindPopup(popup_8020cfb6c785b6f1d89cd19c546b662b)
        ;

        
    
    
            circle_marker_4146ec795c12c92b1b820acf6e5d5479.bindTooltip(
                `<div>
                     ITAPIRA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_26efd55e8577eecd82f993abab04b10f = L.circleMarker(
                [-20.4644, -45.4264],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_92500adb41a50a4a11220051df00761f = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_7b927ff161de95de1021ce285d357eab = $(`<div id="html_7b927ff161de95de1021ce285d357eab" style="width: 100.0%; height: 100.0%;"><b>FORMIGA (MG)</b><br>1 registros</div>`)[0];
                popup_92500adb41a50a4a11220051df00761f.setContent(html_7b927ff161de95de1021ce285d357eab);
            
        

        circle_marker_26efd55e8577eecd82f993abab04b10f.bindPopup(popup_92500adb41a50a4a11220051df00761f)
        ;

        
    
    
            circle_marker_26efd55e8577eecd82f993abab04b10f.bindTooltip(
                `<div>
                     FORMIGA: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_0a39e5c36e30a2cebd67aa4c6c67b674 = L.circleMarker(
                [-20.2331, -46.3731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_a4bee1333155f91cddd5988039ea9406 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_04fd53e77b226bef42e458dc9b46bc43 = $(`<div id="html_04fd53e77b226bef42e458dc9b46bc43" style="width: 100.0%; height: 100.0%;"><b>SAO ROQUE DE MINAS (MG)</b><br>1 registros</div>`)[0];
                popup_a4bee1333155f91cddd5988039ea9406.setContent(html_04fd53e77b226bef42e458dc9b46bc43);
            
        

        circle_marker_0a39e5c36e30a2cebd67aa4c6c67b674.bindPopup(popup_a4bee1333155f91cddd5988039ea9406)
        ;

        
    
    
            circle_marker_0a39e5c36e30a2cebd67aa4c6c67b674.bindTooltip(
                `<div>
                     SAO ROQUE DE MINAS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_e692fb1d2f39f83dded551a8ad28b5db = L.circleMarker(
                [-23.0281, -46.9731],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_7555f3f958f30e6a3e7a9ff4bfbec081 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a3e3b57d078e51607ffe2c1c064c7187 = $(`<div id="html_a3e3b57d078e51607ffe2c1c064c7187" style="width: 100.0%; height: 100.0%;"><b>VINHEDO (SP)</b><br>1 registros</div>`)[0];
                popup_7555f3f958f30e6a3e7a9ff4bfbec081.setContent(html_a3e3b57d078e51607ffe2c1c064c7187);
            
        

        circle_marker_e692fb1d2f39f83dded551a8ad28b5db.bindPopup(popup_7555f3f958f30e6a3e7a9ff4bfbec081)
        ;

        
    
    
            circle_marker_e692fb1d2f39f83dded551a8ad28b5db.bindTooltip(
                `<div>
                     VINHEDO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b67896de51e6789e6d2f45248b2cce41 = L.circleMarker(
                [-22.9068, -43.1729],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "red", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_09af8aeb2743f4bf57fb3f4579d2f9ed = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_9cefa74853d888c80988708dd938c7aa = $(`<div id="html_9cefa74853d888c80988708dd938c7aa" style="width: 100.0%; height: 100.0%;"><b>RIO DE JANEIRO (RJ)</b><br>1 registros</div>`)[0];
                popup_09af8aeb2743f4bf57fb3f4579d2f9ed.setContent(html_9cefa74853d888c80988708dd938c7aa);
            
        

        circle_marker_b67896de51e6789e6d2f45248b2cce41.bindPopup(popup_09af8aeb2743f4bf57fb3f4579d2f9ed)
        ;

        
    
    
            circle_marker_b67896de51e6789e6d2f45248b2cce41.bindTooltip(
                `<div>
                     RIO DE JANEIRO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_950485c486f221aec0dd87bad74018b0 = L.circleMarker(
                [-23.2031, -47.2831],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_0bae9e457dc17c69b55dcb6b41821dda = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_00e948feb2cceb019015348e0a0809a5 = $(`<div id="html_00e948feb2cceb019015348e0a0809a5" style="width: 100.0%; height: 100.0%;"><b>SALTO (SP)</b><br>1 registros</div>`)[0];
                popup_0bae9e457dc17c69b55dcb6b41821dda.setContent(html_00e948feb2cceb019015348e0a0809a5);
            
        

        circle_marker_950485c486f221aec0dd87bad74018b0.bindPopup(popup_0bae9e457dc17c69b55dcb6b41821dda)
        ;

        
    
    
            circle_marker_950485c486f221aec0dd87bad74018b0.bindTooltip(
                `<div>
                     SALTO: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_95920c5e2dd49323547fdf961a941146 = L.circleMarker(
                [-16.7353, -43.8619],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_75101d1afd9b3a01f2e50ec13fac59b8 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_03d8b8479f83634c1ad38d92fc3ad977 = $(`<div id="html_03d8b8479f83634c1ad38d92fc3ad977" style="width: 100.0%; height: 100.0%;"><b>MONTES CLAROS (MG)</b><br>1 registros</div>`)[0];
                popup_75101d1afd9b3a01f2e50ec13fac59b8.setContent(html_03d8b8479f83634c1ad38d92fc3ad977);
            
        

        circle_marker_95920c5e2dd49323547fdf961a941146.bindPopup(popup_75101d1afd9b3a01f2e50ec13fac59b8)
        ;

        
    
    
            circle_marker_95920c5e2dd49323547fdf961a941146.bindTooltip(
                `<div>
                     MONTES CLAROS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_b6ddf4ef2b7a697f14afc7f9ced3303e = L.circleMarker(
                [-19.9317, -44.0536],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "green", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_e86d38d88c23fc24d1650a828ed4d460 = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_1a5bc3328ad00b94c6b43c8ef7c8a031 = $(`<div id="html_1a5bc3328ad00b94c6b43c8ef7c8a031" style="width: 100.0%; height: 100.0%;"><b>CONTAGEM (MG)</b><br>1 registros</div>`)[0];
                popup_e86d38d88c23fc24d1650a828ed4d460.setContent(html_1a5bc3328ad00b94c6b43c8ef7c8a031);
            
        

        circle_marker_b6ddf4ef2b7a697f14afc7f9ced3303e.bindPopup(popup_e86d38d88c23fc24d1650a828ed4d460)
        ;

        
    
    
            circle_marker_b6ddf4ef2b7a697f14afc7f9ced3303e.bindTooltip(
                `<div>
                     CONTAGEM: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
    
            var circle_marker_5e5cc1232d140ba3fe59bf1a19fb00c8 = L.circleMarker(
                [-22.6631, -50.4131],
                {"bubblingMouseEvents": true, "color": "black", "dashArray": null, "dashOffset": null, "fill": true, "fillColor": "blue", "fillOpacity": 0.7, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "opacity": 1.0, "radius": 5, "stroke": true, "weight": 1}
            ).addTo(map_484f75ba95c63d0f96f982f39b06acbb);
        
    
        var popup_3ab14747123f23e37ff3cffb6c9d1feb = L.popup({
  "maxWidth": "100%",
});

        
            
                var html_a042242039705570ef565a109d65f8b4 = $(`<div id="html_a042242039705570ef565a109d65f8b4" style="width: 100.0%; height: 100.0%;"><b>ASSIS (SP)</b><br>1 registros</div>`)[0];
                popup_3ab14747123f23e37ff3cffb6c9d1feb.setContent(html_a042242039705570ef565a109d65f8b4);
            
        

        circle_marker_5e5cc1232d140ba3fe59bf1a19fb00c8.bindPopup(popup_3ab14747123f23e37ff3cffb6c9d1feb)
        ;

        
    
    
            circle_marker_5e5cc1232d140ba3fe59bf1a19fb00c8.bindTooltip(
                `<div>
                     ASSIS: 1
                 </div>`,
                {
  "sticky": true,
}
            );
        
</script>
</html>